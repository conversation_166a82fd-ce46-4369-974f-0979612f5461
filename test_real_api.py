#!/usr/bin/env python3
"""
测试真实的API调用
"""

import sys
import os
import requests
import json

sys.path.append(os.path.dirname(os.path.abspath(__file__)))


def test_real_api():
    """测试真实的API调用"""
    print("🚀 测试真实的API调用...")

    # 使用简化API进行测试
    url = "http://localhost:8088/api/v1/check-compliance-simple"

    # 准备测试数据
    test_data = {
        "procurement_project_type": "服务类",
        "project_category": "政府采购",
        "bidding_procurement_method": "公开招标",
        "file_url": "http://localhost:9090/api/v1/download-shared-object/aHR0cDovLzEyNy4wLjAuMTo5MDAwL3poYW9iaWFvd2VuamlhbmppYW5jaGF6aHVzaG91LyVFNSU4RCU5NyVFNSU4QyVCQSVFNiVCMSVBMSVFNiVCMCVCNCVFNSU4QyVCQiVFNSVCQSU5RiVFMyU4MCU4MSVFNyU5NCU5RiVFNiVCNCVCQiVFNSU5RSU4MyVFNSU5QyVCRSVFNCVCOCU4MCVFNCVCRCU5MyVFNSU4QyU5NiVFNiU4OSU5OCVFNyVBRSVBMSVFNiU5QyU4RCVFNSU4QSVBMSVFOSVBMSVCOSVFNyU5QiVBRSVFNCVCQSU4QyVFNiVBQyVBMSVFNiU4QiU5QiVFNiVBMCU4NyVFNiU5NiU4NyVFNCVCQiVCNiVFRiVCQyU4OCVFNSU4RiU5MSVFNSU5NCVBRSVFNyU4OSU4OCVFRiVCQyU4OS5kb2N4P1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9RVpTNjlMMTlFNjhDS1dSSzVKMTQlMkYyMDI1MDgwOCUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA4MDhUMDEyMDA0WiZYLUFtei1FeHBpcmVzPTQzMTk4JlgtQW16LVNlY3VyaXR5LVRva2VuPWV5SmhiR2NpT2lKSVV6VXhNaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpoWTJObGMzTkxaWGtpT2lKRldsTTJPVXd4T1VVMk9FTkxWMUpMTlVveE5DSXNJbVY0Y0NJNk1UYzFORFkxT1RBek15d2ljR0Z5Wlc1MElqb2ljR1Z1WjJOb1lXNW5JbjAuQXBLbDZqTHVDVmZlWnJiazJpUjBEYmw5d0xCUWVzVURGTnRRUjB3cWg0UjllaGc0bFc4MmlKcnJPb2RLaGdiUTBUY1c5N1hjeE11MHhMMTV6clpRTGcmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JnZlcnNpb25JZD1udWxsJlgtQW16LVNpZ25hdHVyZT00ZDUyMTFhYzE0ZWM5ZmMyYzdkZDMxNGQzNGVhOGRlYzhlZmY5MTJiZmNkNTQzYzM1NDBiNzIyM2MxY2UxZGRk",
    }

    print(f"请求URL: {url}")
    print(f"请求数据: {json.dumps(test_data, ensure_ascii=False, indent=2)}")

    try:
        print("正在发送请求...")
        # 发送请求
        response = requests.post(url, json=test_data, timeout=120)
        print("请求完成")

        print(f"\n响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")

        # 解析响应
        try:
            response_data = response.json()
            print(f"\n✅ 响应JSON解析成功!")
            print(f"响应结构: {list(response_data.keys())}")

            # 检查是否是错误响应
            if response.status_code != 200:
                print(f"❌ API返回错误:")
                print(f"   错误消息: {response_data.get('message', 'Unknown error')}")
                print(f"   错误代码: {response_data.get('error_code', 'Unknown')}")
                return False

            if "data" in response_data:
                data = response_data["data"]
                print(f"数据结构: {list(data.keys())}")

                if "sensitiveWordsArr" in data:
                    print(f"敏感词数量: {len(data['sensitiveWordsArr'])}")

                if "checkResultArr" in data:
                    print(f"检查结果数量: {len(data['checkResultArr'])}")

                    # 显示前几个问题
                    for i, item in enumerate(data["checkResultArr"][:3]):
                        print(
                            f"问题{i+1}: {item.get('quesType')} - {item.get('quesDesc', '')[:50]}..."
                        )

            return True

        except json.JSONDecodeError as e:
            print(f"❌ 响应JSON解析失败: {e}")
            print(f"原始响应: {response.text[:500]}...")
            return False

    except requests.exceptions.ConnectionError:
        print("❌ 连接失败，请确保服务器正在运行")
        return False
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
        return False
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False


def test_health_check():
    """测试健康检查"""
    print("\n🔍 测试健康检查...")

    try:
        response = requests.get("http://localhost:8088/health", timeout=10)
        print(f"健康检查状态码: {response.status_code}")

        if response.status_code == 200:
            print("✅ 服务器运行正常")
            return True
        else:
            print("❌ 服务器状态异常")
            return False

    except Exception as e:
        print(f"❌ 健康检查失败: {e}")
        return False


if __name__ == "__main__":
    print("🚀 开始测试真实API...")

    # 先测试健康检查
    if not test_health_check():
        print("\n❌ 服务器未运行，请先启动服务器")
        sys.exit(1)

    # 测试API调用
    success = test_real_api()

    if success:
        print("\n🎉 API测试成功!")
        print("\n📋 测试结果:")
        print("✅ 服务器运行正常")
        print("✅ API响应格式正确")
        print("✅ JSON解析成功")
        print("\n现在可以检查AI模型是否正确返回了合规性问题！")
    else:
        print("\n❌ API测试失败")
        sys.exit(1)
