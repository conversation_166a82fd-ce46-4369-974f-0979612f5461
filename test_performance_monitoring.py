#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试性能监控功能
"""

import sys
import os
import time
from unittest.mock import Mock, patch

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.logger import performance_monitor
from app.services.compliance_service import ComplianceService


def test_performance_monitor_basic():
    """测试性能监控器基本功能"""
    print("测试性能监控器基本功能...")

    # 模拟记录各阶段性能
    request_id = "test-1"

    # 记录各个阶段的性能
    performance_monitor.record_stage_performance(
        request_id, "file_processing", 2.5, True
    )
    performance_monitor.record_stage_performance(
        request_id, "ai_model_call", 15.0, True
    )
    performance_monitor.record_stage_performance(
        request_id, "sensitive_word", 3.2, True
    )
    performance_monitor.record_stage_performance(
        request_id, "result_aggregation", 0.8, True
    )
    performance_monitor.record_stage_performance(
        request_id, "total_pipeline", 21.5, True
    )

    # 获取请求指标
    metrics = performance_monitor.get_request_metrics(request_id)

    assert len(metrics) == 5
    assert "file_processing" in metrics
    assert "ai_model_call" in metrics
    assert metrics["ai_model_call"]["duration"] == 15.0
    assert metrics["ai_model_call"]["success"] == True

    print("✅ 性能监控器基本功能测试通过")


def test_performance_analysis():
    """测试性能分析功能"""
    print("\n测试性能分析功能...")

    request_id = "test-2"

    # 记录一些性能数据，包括超过阈值的情况
    performance_monitor.record_stage_performance(
        request_id, "file_processing", 35.0, True
    )  # 超过30秒阈值
    performance_monitor.record_stage_performance(request_id, "ai_model_call", 5.0, True)
    performance_monitor.record_stage_performance(
        request_id, "sensitive_word", 2.0, False
    )  # 失败
    performance_monitor.record_stage_performance(
        request_id, "result_aggregation", 1.0, True
    )
    performance_monitor.record_stage_performance(
        request_id, "total_pipeline", 43.0, True
    )

    # 分析性能
    analysis = performance_monitor.analyze_performance(request_id)

    assert analysis["request_id"] == request_id
    assert analysis["total_stages"] == 4  # 只计算实际处理阶段，不包括total_pipeline
    assert (
        analysis["successful_stages"] == 3
    )  # file_processing, ai_model_call, result_aggregation
    assert analysis["failed_stages"] == 1  # sensitive_word
    assert analysis["slowest_stage"]["name"] == "file_processing"
    assert analysis["slowest_stage"]["duration"] == 35.0
    assert len(analysis["bottlenecks"]) > 0  # 应该检测到瓶颈
    assert len(analysis["recommendations"]) > 0  # 应该有优化建议

    print("✅ 性能分析功能测试通过")


def test_global_stats():
    """测试全局统计功能"""
    print("\n测试全局统计功能...")

    # 获取全局统计
    stats = performance_monitor.get_global_stats()

    assert "total_requests" in stats
    assert "success_rate" in stats
    assert "average_duration" in stats
    assert "stage_averages" in stats

    # 应该有之前测试记录的数据
    assert stats["total_requests"] >= 2
    assert "file_processing" in stats["stage_averages"]

    print("✅ 全局统计功能测试通过")


def test_compliance_service_metrics():
    """测试合规性检查服务的性能指标获取"""
    print("\n测试合规性检查服务性能指标...")

    service = ComplianceService()

    # 获取处理指标
    metrics = service.get_processing_metrics()

    assert "pipeline_metrics" in metrics
    assert "service_health" in metrics
    assert "performance_stats" in metrics
    assert "component_info" in metrics

    # 检查组件信息
    component_info = metrics["component_info"]
    assert "file_processor" in component_info
    assert "ai_model" in component_info
    assert "sensitive_word" in component_info
    assert "result_processor" in component_info

    print("✅ 合规性检查服务性能指标测试通过")


def test_request_performance_analysis():
    """测试特定请求的性能分析"""
    print("\n测试特定请求性能分析...")

    service = ComplianceService()
    request_id = "test-1"  # 使用之前记录的请求ID

    # 获取特定请求的性能分析
    analysis = service.get_request_performance_analysis(request_id)

    assert "request_id" in analysis
    assert analysis["request_id"] == request_id
    assert "total_stages" in analysis
    assert "slowest_stage" in analysis
    assert "fastest_stage" in analysis

    print("✅ 特定请求性能分析测试通过")


def test_performance_cleanup():
    """测试性能数据清理功能"""
    print("\n测试性能数据清理...")

    service = ComplianceService()

    # 记录当前的请求数量
    stats_before = performance_monitor.get_global_stats()
    requests_before = stats_before.get("total_requests", 0)

    # 执行清理（使用0小时，这样会清理所有数据）
    service.cleanup_performance_data(max_age_hours=0)

    # 检查清理后的状态
    stats_after = performance_monitor.get_global_stats()

    # 应该清理了一些数据或者返回无数据消息
    if "message" in stats_after:
        assert stats_after["message"] == "暂无性能数据"
    else:
        # 如果还有数据，应该比之前少
        requests_after = stats_after.get("total_requests", 0)
        assert requests_after <= requests_before

    print("✅ 性能数据清理测试通过")


if __name__ == "__main__":
    print("性能监控功能测试")
    print("=" * 50)

    try:
        test_performance_monitor_basic()
        test_performance_analysis()
        test_global_stats()
        test_compliance_service_metrics()
        test_request_performance_analysis()
        test_performance_cleanup()

        print("\n🎉 所有测试通过！性能监控功能实现成功")

    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback

        traceback.print_exc()
        sys.exit(1)
