2025-08-12 16:56:15 | ERROR    | app.core.logger:__exit__:421 | 操作 AI模型调用(尝试1) 发生异常: Request timed out.
2025-08-12 16:56:15 | ERROR    | app.core.error_logger:log_service_error:59 | 服务错误 | AIModelService.call_model_with_retry | ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257 | 错误: AIModelError: AI模型调用异常: Request timed out. (模型: gemini-2.5-flash, 原因: Request timed out.)
2025-08-12 16:56:15 | ERROR    | app.core.logger:wrapper:452 | 函数调用失败: call_model | 耗时: 15.884秒 | 错误: AI模型调用异常: Request timed out. (模型: gemini-2.5-flash, 原因: Request timed out.)
2025-08-12 16:56:15 | ERROR    | app.services.ai_model_service:check_compliance:1212 | AI模型错误 | 请求ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257 | 错误: AI模型调用异常: Request timed out. (模型: gemini-2.5-flash, 原因: Request timed out.)
2025-08-12 16:56:15 | ERROR    | app.core.logger:wrapper:452 | 函数调用失败: check_compliance | 耗时: 15.888秒 | 错误: AI模型调用异常: Request timed out. (模型: gemini-2.5-flash, 原因: Request timed out.)
2025-08-12 16:56:15 | ERROR    | app.core.logger:__exit__:421 | 操作 AI合规性检查阶段 发生异常: AI模型调用异常: Request timed out. (模型: gemini-2.5-flash, 原因: Request timed out.)
2025-08-12 16:56:15 | ERROR    | app.core.logger:wrapper:452 | 函数调用失败: ai_compliance_check_stage | 耗时: 15.894秒 | 错误: AI合规性检查阶段失败: AI模型调用异常: Request timed out. (模型: gemini-2.5-flash, 原因: Request timed out.)
