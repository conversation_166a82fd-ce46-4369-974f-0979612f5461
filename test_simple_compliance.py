#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的合规性检查测试
"""

import requests
import json


def test_with_mock_file():
    """使用模拟文件测试"""
    print("简化合规性检查测试")
    print("=" * 50)

    base_url = "http://localhost:8088"

    # 使用一个简单的测试内容，不需要真实文件下载
    request_data = {
        "procurement_project_type": "服务类",
        "project_category": "政府采购",
        "bidding_procurement_method": "公开招标",
        "bidding_doc": {
            "filename": "test_document.docx",
            "extension": ".docx",
            "mime_type": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "size": 1024,
            "url": "http://httpbin.org/json",  # 这个URL会返回JSON，不是真实文件
        },
    }

    print("发送请求...")
    print(f"请求数据: {json.dumps(request_data, indent=2, ensure_ascii=False)}")

    try:
        response = requests.post(
            f"{base_url}/api/v1/check-compliance", json=request_data, timeout=60
        )

        print(f"\n状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print(f"响应内容: {response.text}")

        if response.status_code == 200:
            result = response.json()
            print("\n✅ 请求成功")
            print(f"敏感词数量: {len(result.get('sensitiveWordsArr', []))}")
            print(f"检查结果数量: {len(result.get('checkResultArr', []))}")
        else:
            print(f"\n❌ 请求失败: {response.status_code}")

            # 尝试解析错误信息
            try:
                error_data = response.json()
                print(
                    f"错误详情: {json.dumps(error_data, indent=2, ensure_ascii=False)}"
                )
            except:
                print(f"错误文本: {response.text}")

    except requests.exceptions.Timeout:
        print("\n⏰ 请求超时")
    except Exception as e:
        print(f"\n❌ 请求异常: {str(e)}")


if __name__ == "__main__":
    test_with_mock_file()
