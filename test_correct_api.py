#!/usr/bin/env python3
"""
正确的API测试脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_complete_request():
    """测试完整的请求模型"""
    print("🔍 测试完整的请求模型...")
    
    try:
        from app.models.schemas import (
            ComplianceCheckRequest, 
            ComplianceCheckResponse, 
            ComplianceCheckData,
            FileInfo
        )
        from app.models.enums import (
            ProcurementProjectType,
            ProjectCategory,
            BiddingProcurementMethod,
            FileExtension,
            MimeType
        )
        
        # 创建文件信息
        file_info = FileInfo(
            filename="test.docx",
            extension=FileExtension.DOCX,
            mime_type=MimeType.DOCX,
            size=1024,
            url="http://example.com/test.docx"
        )
        
        # 创建完整的请求
        request = ComplianceCheckRequest(
            bidding_doc=file_info,
            procurement_project_type=ProcurementProjectType.SERVICE,
            project_category=ProjectCategory.GOVERNMENT_PROCUREMENT,
            bidding_procurement_method=BiddingProcurementMethod.PUBLIC_BIDDING
        )
        
        print(f"✅ 完整请求模型创建成功:")
        print(f"   文件名: {request.bidding_doc.filename}")
        print(f"   采购类型: {request.procurement_project_type}")
        print(f"   项目类别: {request.project_category}")
        print(f"   招标方式: {request.bidding_procurement_method}")
        
        # 测试获取项目信息
        project_info = request.get_project_info()
        print(f"   项目信息提取: ✅")
        
        return True
        
    except Exception as e:
        print(f"❌ 完整请求模型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_simple_request():
    """测试简化的请求模型"""
    print("\n🔍 测试简化的请求模型...")
    
    try:
        from app.models.schemas import SimpleComplianceCheckRequest
        
        # 创建简化的请求
        simple_request = SimpleComplianceCheckRequest(
            procurement_project_type="服务类",
            project_category="政府采购",
            bidding_procurement_method="公开招标",
            file_url="http://example.com/test.docx"
        )
        
        print(f"✅ 简化请求模型创建成功:")
        print(f"   采购类型: {simple_request.procurement_project_type}")
        print(f"   项目类别: {simple_request.project_category}")
        print(f"   招标方式: {simple_request.bidding_procurement_method}")
        print(f"   文件URL: {simple_request.file_url}")
        
        return True
        
    except Exception as e:
        print(f"❌ 简化请求模型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_response_format():
    """测试响应格式"""
    print("\n🔍 测试响应格式...")
    
    try:
        from app.models.schemas import ComplianceCheckResponse, ComplianceCheckData, SensitiveWordItem, CheckResultItem
        from app.models.enums import QuestionType
        
        # 创建完整的响应
        response = ComplianceCheckResponse(
            code=200,
            message="检查完成",
            data=ComplianceCheckData(
                sensitiveWordsArr=[
                    SensitiveWordItem(type="错漏词汇", content="测试词", num=1)
                ],
                checkResultArr=[
                    CheckResultItem(
                        quesType=QuestionType.COMPLIANCE,
                        quesDesc="测试问题",
                        originalArr=["原文"],
                        point="要点",
                        advice="建议"
                    )
                ]
            )
        )
        
        # 测试正确的属性访问
        sensitive_count = len(response.data.sensitiveWordsArr)
        check_count = len(response.data.checkResultArr)
        
        print(f"✅ 响应格式正确:")
        print(f"   敏感词数量: {sensitive_count}")
        print(f"   检查结果数量: {check_count}")
        
        # 测试JSON序列化
        json_data = response.model_dump()
        print(f"   JSON结构: {list(json_data.keys())}")
        print(f"   data结构: {list(json_data['data'].keys())}")
        
        return True
    except Exception as e:
        print(f"❌ 响应格式测试失败: {e}")
        return False

def test_api_endpoints():
    """测试API端点信息"""
    print("\n🔍 API端点信息...")
    
    print("✅ 可用的API端点:")
    print("   1. POST /api/v1/check-compliance")
    print("      - 使用: ComplianceCheckRequest")
    print("      - 需要: bidding_doc, procurement_project_type, project_category, bidding_procurement_method")
    print("   2. POST /api/v1/check-compliance-simple") 
    print("      - 使用: SimpleComplianceCheckRequest")
    print("      - 需要: procurement_project_type, project_category, bidding_procurement_method, file_url")
    print("   3. 响应格式: {code, message, data: {sensitiveWordsArr, checkResultArr}}")
    
    return True

if __name__ == "__main__":
    print("🚀 开始正确的API测试...")
    
    success = True
    success &= test_complete_request()
    success &= test_simple_request()
    success &= test_response_format()
    success &= test_api_endpoints()
    
    if success:
        print("\n🎉 所有测试通过！API模型结构正确！")
        print("\n📋 使用指南:")
        print("✅ 完整API: 使用 ComplianceCheckRequest 模型")
        print("✅ 简化API: 使用 SimpleComplianceCheckRequest 模型")
        print("✅ 响应格式: 统一的 {code, message, data} 结构")
        print("✅ 属性访问: 使用 response.data.xxx 格式")
        print("\n现在可以使用正确的请求格式测试API了！")
    else:
        print("\n❌ 部分测试失败")
        sys.exit(1)
