#!/usr/bin/env python3
"""
测试JSON修复功能
"""

import sys
import os
import json

sys.path.append(os.path.dirname(os.path.abspath(__file__)))


def test_json_fix():
    """测试JSON修复功能"""
    print("🔍 测试JSON修复功能...")

    try:
        from app.services.ai_model_service import ai_model_service
        from app.models.schemas import CheckResultItem, QuestionType
        
        # 模拟有问题的AI响应（包含中文引号）
        problematic_response = """{
  "checkResultArr": [
    {
      "quesType": "合规性/逻辑性/规范性",
      "quesDesc": "招标文件封面项目名称多出"阿萨德"字样，与招标公告及其他章节的项目名称不一致，存在笔误。",
      "originalArr": [
        "安徽理工大学第一附属医院南区污水医废、生活垃圾一体化托管服务项目二次阿萨德"
      ],
      "point": "项目信息核验，确保文件中的项目名称与招标公告完全一致。",
      "advice": "删除招标文件封面项目名称中多余的"阿萨德"字样，使其与招标公告及其他章节的项目名称保持一致。"
    }
  ]
}"""

        print(f"原始响应长度: {len(problematic_response)} 字符")
        print("原始响应包含中文引号:", "阿萨德" in problematic_response)
        
        # 1. 测试JSON清理
        print("\n1. 测试JSON清理...")
        cleaned = ai_model_service.clean_json_data(problematic_response)
        print(f"清理后长度: {len(cleaned)} 字符")
        
        # 2. 测试JSON解析
        print("\n2. 测试JSON解析...")
        parsed_data = json.loads(cleaned)
        print(f"解析成功，checkResultArr长度: {len(parsed_data['checkResultArr'])}")
        
        # 3. 测试字段提取
        print("\n3. 测试字段提取...")
        item = parsed_data["checkResultArr"][0]
        
        ques_type = item.get("quesType", "")
        ques_desc = item.get("quesDesc", "")
        original_arr = item.get("originalArr", [])
        point = item.get("point", "")
        advice = item.get("advice", "")
        
        print(f"quesType: '{ques_type}' (长度: {len(ques_type)})")
        print(f"quesDesc: '{ques_desc[:50]}...' (长度: {len(ques_desc)})")
        print(f"point: '{point[:50]}...' (长度: {len(point)})")
        print(f"advice: '{advice[:50]}...' (长度: {len(advice)})")
        print(f"originalArr: {len(original_arr)} 个元素")
        
        # 检查是否有空字段
        empty_fields = []
        if not ques_desc.strip():
            empty_fields.append("quesDesc")
        if not point.strip():
            empty_fields.append("point")
        if not advice.strip():
            empty_fields.append("advice")
            
        if empty_fields:
            print(f"❌ 发现空字段: {empty_fields}")
            return False
        else:
            print("✅ 所有必要字段都有内容")
        
        # 4. 测试CheckResultItem创建
        print("\n4. 测试CheckResultItem创建...")
        
        # 处理组合类型
        if "/" in ques_type:
            ques_type = ques_type.split("/")[0].strip()
            
        # 验证问题类型
        valid_types = [e.value for e in QuestionType]
        if ques_type not in valid_types:
            ques_type = QuestionType.STANDARDIZATION.value
            
        check_result = CheckResultItem(
            quesType=ques_type,
            quesDesc=ques_desc,
            originalArr=original_arr,
            point=point,
            advice=advice,
        )
        
        print(f"✅ CheckResultItem创建成功")
        print(f"  quesType: '{check_result.quesType}'")
        print(f"  quesDesc: '{check_result.quesDesc[:50]}...'")
        print(f"  point: '{check_result.point[:50]}...'")
        print(f"  advice: '{check_result.advice[:50]}...'")
        
        # 5. 测试验证逻辑
        print("\n5. 测试验证逻辑...")
        from app.services.result_processor import result_processor
        
        validated_results = result_processor.validate_check_results([check_result])
        print(f"验证结果: 输入1个，输出{len(validated_results)}个")
        
        if len(validated_results) == 0:
            print("❌ 结果被验证逻辑丢弃了！")
            return False
        else:
            print("✅ 验证逻辑通过")
            
        print("\n🎉 所有测试通过！修复成功！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_json_fix()
    if success:
        print("\n✅ JSON修复功能正常工作")
    else:
        print("\n❌ JSON修复功能仍有问题")
    sys.exit(0 if success else 1)
