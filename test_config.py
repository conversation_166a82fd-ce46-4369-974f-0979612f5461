#!/usr/bin/env python3
"""
测试配置是否正确加载
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.config import settings

def test_config():
    """测试配置"""
    print("🔍 测试配置加载...")
    
    print(f"max_output_tokens: {settings.max_output_tokens}")
    print(f"model_name: {settings.model_name}")
    print(f"model_url: {settings.model_url}")
    print(f"max_context_length: {settings.max_context_length}")
    
    # 测试AI模型服务的配置
    from app.services.ai_model_service import AIModelService
    ai_service = AIModelService()
    
    print(f"AI服务配置:")
    print(f"  max_output_tokens: {ai_service.max_output_tokens}")

if __name__ == "__main__":
    test_config()
