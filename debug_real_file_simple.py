#!/usr/bin/env python3
"""
简化的真实文件AI响应调试脚本
"""

import sys
import os
import json
import requests
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 设置详细的日志
logging.basicConfig(
    level=logging.DEBUG,
    format="%(asctime)s | %(levelname)s | %(message)s",
)


def debug_real_file_simple():
    """简化的真实文件AI响应调试"""
    print("开始简化的真实文件AI响应调试...")

    try:
        # 直接调用API并获取详细的调试信息
        test_data = {
            "procurement_project_type": "服务类",
            "project_category": "政府采购",
            "bidding_procurement_method": "公开招标",
            "file_url": "http://localhost:9090/api/v1/download-shared-object/aHR0cDovLzEyNy4wLjAuMTo5MDAwL3poYW9iaWFvd2VuamlhbmppYW5jaGF6aHVzaG91LyVFNSU4RCU5NyVFNSU4QyVCQSVFNiVCMSVBMSVFNiVCMCVCNCVFNSU4QyVCQiVFNSVCQSU5RiVFMyU4MCU4MSVFNyU5NCU5RiVFNiVCNCVCQiVFNSU5RSU4MyVFNSU5QyVCRSVFNCVCOCU4MCVFNCVCRCU5MyVFNSU4QyU5NiVFNiU4OSU5OCVFNyVBRSVBMSVFNiU5QyU4RCVFNSU4QSVBMSVFOSVBMSVCOSVFNyU5QiVBRSVFNCVCQSU4QyVFNiVBQyVBMSVFNiU4QiU5QiVFNiVBMCU4NyVFNiU5NiU4NyVFNCVCQiVCNiVFRiVCQyU4OCVFNSU4RiU5MSVFNSU5NCVBRSVFNyU4OSU4OCVFRiVCQyU4OS5kb2N4P1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9RVpTNjlMMTlFNjhDS1dSSzVKMTQlMkYyMDI1MDgwOCUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA4MDhUMDEyMDA0WiZYLUFtei1FeHBpcmVzPTQzMTk4JlgtQW16LVNlY3VyaXR5LVRva2VuPWV5SmhiR2NpT2lKSVV6VXhNaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpoWTJObGMzTkxaWGtpT2lKRldsTTJPVXd4T1VVMk9FTkxWMUpMTlVveE5DSXNJbVY0Y0NJNk1UYzFORFkxT1RBek15d2ljR0Z5Wlc1MElqb2ljR1Z1WjJOb1lXNW5JbjAuQXBLbDZqTHVDVmZlWnJiazJpUjBEYmw5d0xCUWVzVURGTnRRUjB3cWg0UjllaGc0bFc4MmlKcnJPb2RLaGdiUTBUY1c5N1hjeE11MHhMMTV6clpRTGcmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JnZlcnNpb25JZD1udWxsJlgtQW16LVNpZ25hdHVyZT00ZDUyMTFhYzE0ZWM5ZmMyYzdkZDMxNGQzNGVhOGRlYzhlZmY5MTJiZmNkNTQzYzM1NDBiNzIyM2MxY2UxZGRk",
        }

        print("发送请求到API...")
        response = requests.post(
            "http://localhost:8088/api/v1/check-compliance-simple",
            json=test_data,
            headers={"Content-Type": "application/json"},
            timeout=180,
        )

        print(f"API响应状态码: {response.status_code}")

        if response.status_code == 200:
            result = response.json()
            print(f"敏感词数量: {len(result.get('sensitiveWordsArr', []))}")
            print(f"检查结果数量: {len(result.get('checkResultArr', []))}")

            if len(result.get("checkResultArr", [])) == 0:
                print("\n❌ 检查结果为0，这是问题所在！")
                print("让我们分析可能的原因...")

                # 检查日志文件
                print("\n1. 检查最新的日志文件...")
                log_dir = "logs"
                if os.path.exists(log_dir):
                    log_files = []
                    for file in os.listdir(log_dir):
                        if file.endswith(".log") and "2025-08-08" in file:
                            file_path = os.path.join(log_dir, file)
                            log_files.append((file_path, os.path.getmtime(file_path)))

                    if log_files:
                        latest_log = sorted(
                            log_files, key=lambda x: x[1], reverse=True
                        )[0][0]
                        print(f"分析日志文件: {latest_log}")

                        try:
                            with open(latest_log, "r", encoding="utf-8") as f:
                                lines = f.readlines()

                            # 查找最近的AI模型调用相关日志
                            ai_logs = []
                            for line in lines[-500:]:  # 最后500行
                                if "AI模型" in line or "JSON" in line or "响应" in line:
                                    ai_logs.append(line.strip())

                            if ai_logs:
                                print("最近的AI相关日志:")
                                for log_line in ai_logs[-10:]:  # 最后10条
                                    print(f"  {log_line}")
                            else:
                                print("未找到AI相关日志")

                        except Exception as e:
                            print(f"读取日志失败: {e}")
                else:
                    print("日志目录不存在")

                # 尝试直接测试AI模型
                print("\n2. 直接测试AI模型...")
                try:
                    from app.services.ai_model_service import AIModelService

                    ai_service = AIModelService()

                    # 简单测试
                    test_messages = [
                        {
                            "role": "system",
                            "content": '请返回一个简单的JSON: {"test": "success"}',
                        },
                        {"role": "user", "content": "请按要求返回JSON"},
                    ]

                    raw_response = ai_service.call_model(test_messages, "debug-simple")
                    print(f"AI原始响应: '{raw_response}'")

                    cleaned = ai_service.clean_json_data(raw_response)
                    print(f"清理后响应: '{cleaned}'")

                    try:
                        parsed = json.loads(cleaned)
                        print(f"✅ JSON解析成功: {parsed}")
                    except Exception as parse_error:
                        print(f"❌ JSON解析失败: {parse_error}")

                except Exception as e:
                    print(f"AI模型测试失败: {e}")

                # 分析可能的原因
                print("\n3. 可能的原因分析:")
                print("   a) AI模型返回的内容不是有效的JSON格式")
                print("   b) AI模型返回了拒绝性回复（如'抱歉，我无法...'）")
                print("   c) 文件内容过长，AI模型无法处理")
                print("   d) AI模型的提示词需要进一步优化")

                return False
            else:
                print("✅ 检查结果正常")
                return True
        else:
            print(f"❌ API调用失败: {response.text}")
            return False

    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_ai_with_file_content():
    """使用文件内容直接测试AI模型"""
    print("\n=== 使用文件内容直接测试AI模型 ===")

    try:
        # 下载文件
        file_url = "http://localhost:9090/api/v1/download-shared-object/aHR0cDovLzEyNy4wLjAuMTo5MDAwL3poYW9iaWFvd2VuamlhbmppYW5jaGF6aHVzaG91LyVFNSU4RCU5NyVFNSU4QyVCQSVFNiVCMSVBMSVFNiVCMCVCNCVFNSU4QyVCQiVFNSVCQSU5RiVFMyU4MCU4MSVFNyU5NCU5RiVFNiVCNCVCQiVFNSU5RSU4MyVFNSU5QyVCRSVFNCVCOCU4MCVFNCVCRCU5MyVFNSU4QyU5NiVFNiU4OSU5OCVFNyVBRSVBMSVFNiU5QyU4RCVFNSU4QSVBMSVFOSVBMSVCOSVFNyU5QiVBRSVFNCVCQSU4QyVFNiVBQyVBMSVFNiU4QiU5QiVFNiVBMCU4NyVFNiU5NiU4NyVFNCVCQiVCNiVFRiVCQyU4OCVFNSU4RiU5MSVFNSU5NCVBRSVFNyU4OSU4OCVFRiVCQyU4OS5kb2N4P1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9RVpTNjlMMTlFNjhDS1dSSzVKMTQlMkYyMDI1MDgwOCUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA4MDhUMDEyMDA0WiZYLUFtei1FeHBpcmVzPTQzMTk4JlgtQW16LVNlY3VyaXR5LVRva2VuPWV5SmhiR2NpT2lKSVV6VXhNaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpoWTJObGMzTkxaWGtpT2lKRldsTTJPVXd4T1VVMk9FTkxWMUpMTlVveE5DSXNJbVY0Y0NJNk1UYzFORFkxT1RBek15d2ljR0Z5Wlc1MElqb2ljR1Z1WjJOb1lXNW5JbjAuQXBLbDZqTHVDVmZlWnJiazJpUjBEYmw5d0xCUWVzVURGTnRRUjB3cWg0UjllaGc0bFc4MmlKcnJPb2RLaGdiUTBUY1c5N1hjeE11MHhMMTV6clpRTGcmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JnZlcnNpb25JZD1udWxsJlgtQW16LVNpZ25hdHVyZT00ZDUyMTFhYzE0ZWM5ZmMyYzdkZDMxNGQzNGVhOGRlYzhlZmY5MTJiZmNkNTQzYzM1NDBiNzIyM2MxY2UxZGRk"

        print("下载文件...")
        response = requests.get(file_url, timeout=30)
        if response.status_code != 200:
            print(f"❌ 文件下载失败: {response.status_code}")
            return False

        print(f"✅ 文件下载成功，大小: {len(response.content)} 字节")

        # 使用python-docx简单提取文本
        try:
            from docx import Document
            import io

            # 保存为临时文件
            temp_file = "temp_test.docx"
            with open(temp_file, "wb") as f:
                f.write(response.content)

            # 提取文本
            doc = Document(temp_file)
            text_content = ""
            for paragraph in doc.paragraphs:
                text_content += paragraph.text + "\n"

            # 清理临时文件
            os.remove(temp_file)

            print(f"✅ 文本提取成功，长度: {len(text_content)} 字符")
            print(f"文本前500字符: {text_content[:500]}...")

            # 截取前5000字符进行测试
            test_content = (
                text_content[:5000] if len(text_content) > 5000 else text_content
            )

            # 直接调用AI模型
            from app.services.ai_model_service import AIModelService
            from app.models.schemas import ProjectInfo
            from app.models.enums import (
                ProcurementProjectType,
                ProjectCategory,
                BiddingProcurementMethod,
            )

            ai_service = AIModelService()

            project_info = ProjectInfo(
                procurement_project_type=ProcurementProjectType.SERVICE,
                project_category=ProjectCategory.GOVERNMENT_PROCUREMENT,
                bidding_procurement_method=BiddingProcurementMethod.PUBLIC_BIDDING,
            )

            print(f"\n直接调用AI模型进行合规性检查...")
            print(f"测试内容长度: {len(test_content)} 字符")

            result = ai_service.check_compliance(
                test_content, project_info, "direct-file-test"
            )

            print(f"✅ AI模型调用成功!")
            print(f"发现问题数量: {len(result.checkResultArr)}")

            if len(result.checkResultArr) > 0:
                print("发现的问题:")
                for i, item in enumerate(result.checkResultArr[:3]):
                    print(f"  {i+1}. {item.quesType}: {item.quesDesc[:100]}...")
                return True
            else:
                print("❌ 仍然未发现问题，需要进一步调试AI模型的提示词")

                # 获取原始AI响应
                print("\n获取AI原始响应...")
                system_prompt = f"""你是一个专业的招标文件合规性检查助手。请仔细审查招标文件内容，识别其中可能存在的合规性问题。

项目信息：
- 采购项目类型：{project_info.procurement_project_type.value}
- 项目类别：{project_info.project_category.value}
- 招标采购方式：{project_info.bidding_procurement_method.value}

请严格按照以下JSON格式返回结果，问题类型必须是以下之一：合规性、逻辑性、风险管理、规范性、公平性、可操作性

{{
    "checkResultArr": [
        {{
            "quesType": "规范性",
            "quesDesc": "问题描述",
            "originalArr": ["原文内容"],
            "point": "问题要点", 
            "advice": "修改建议"
        }}
    ]
}}

如果没有发现问题，请返回：
{{
    "checkResultArr": []
}}"""

                user_prompt = (
                    f"请对以下招标文件进行合规性审查：\n\n{test_content[:3000]}"
                )

                messages = [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt},
                ]

                raw_response = ai_service.call_model(messages, "debug-raw-file")
                print(f"AI原始响应长度: {len(raw_response)} 字符")
                print(f"AI原始响应: {raw_response}")

                return False

        except ImportError:
            print("❌ python-docx未安装，无法提取文档内容")
            print("请运行: pip install python-docx")
            return False
        except Exception as e:
            print(f"❌ 文档处理失败: {e}")
            return False

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback

        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("🔍 开始简化的真实文件AI响应调试...")
    print("=" * 60)

    # 执行调试
    result1 = debug_real_file_simple()
    result2 = test_ai_with_file_content()

    print("\n" + "=" * 60)
    print("📊 调试总结")
    print("=" * 60)

    if result1 and result2:
        print("🎉 调试成功！问题已解决。")
    elif result2:
        print("⚠️  直接测试成功，但API调用仍有问题。")
    else:
        print("❌ 调试发现问题，需要进一步修复。")
        print("\n建议的解决方案:")
        print("1. 检查AI模型的提示词是否合适")
        print("2. 检查文件内容是否被正确处理")
        print("3. 检查AI模型是否返回了有效的JSON格式")


if __name__ == "__main__":
    main()
