#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试敏感词服务数据转换修复
"""

import sys
import os

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.sensitive_word_service import SensitiveWordService


def test_convert_to_target_format():
    """测试_convert_to_target_format方法对不同数据格式的处理"""
    print("测试_convert_to_target_format方法...")

    service = SensitiveWordService()

    # 测试1: 标准字典格式
    dict_results = [
        {"敏感词类型": "政治敏感", "敏感词内容": "测试词1", "出现次数": 2},
        {"敏感词类型": "商业敏感", "敏感词内容": "测试词2", "出现次数": 1},
    ]

    converted = service._convert_to_target_format(dict_results)
    print(f"字典格式转换结果: {len(converted)}个敏感词")
    assert len(converted) == 2
    assert converted[0].content == "测试词1"
    assert converted[0].type == "政治敏感"
    assert converted[0].num == 2

    # 测试2: 英文字段名格式
    english_results = [
        {"type": "政治敏感", "content": "测试词3", "count": 3},
        {"category": "商业敏感", "word": "测试词4", "frequency": 1},
    ]

    converted = service._convert_to_target_format(english_results)
    print(f"英文字段格式转换结果: {len(converted)}个敏感词")
    assert len(converted) == 2
    assert converted[0].content == "测试词3"
    assert converted[1].content == "测试词4"

    # 测试3: 混合格式
    mixed_results = [
        {"敏感词类型": "政治敏感", "content": "测试词5", "出现次数": "2"},  # 字符串数字
        {"type": "商业敏感", "敏感词内容": "测试词6", "num": 1},
    ]

    converted = service._convert_to_target_format(mixed_results)
    print(f"混合格式转换结果: {len(converted)}个敏感词")
    assert len(converted) == 2

    # 测试4: 空结果
    empty_results = []
    converted = service._convert_to_target_format(empty_results)
    print(f"空结果转换: {len(converted)}个敏感词")
    assert len(converted) == 0

    # 测试5: 无效数据
    invalid_results = [
        {"敏感词类型": "政治敏感", "敏感词内容": "", "出现次数": 1},  # 空内容
        {"敏感词类型": "", "敏感词内容": "测试词7", "出现次数": 0},  # 零次数
        None,  # None值
        "invalid_data",  # 字符串
    ]

    converted = service._convert_to_target_format(invalid_results)
    print(f"无效数据转换结果: {len(converted)}个敏感词")
    # 应该只有一个有效结果（测试词7，次数会被修正为1）
    assert len(converted) == 1
    assert converted[0].content == "测试词7"
    assert converted[0].num == 1

    print("✅ _convert_to_target_format方法测试通过")


def test_safe_extract_field():
    """测试_safe_extract_field方法"""
    print("\n测试_safe_extract_field方法...")

    service = SensitiveWordService()

    # 测试字典访问
    dict_data = {"敏感词类型": "政治敏感", "content": "测试内容"}
    result = service._safe_extract_field(dict_data, ["敏感词类型", "type"], "默认")
    assert result == "政治敏感"

    result = service._safe_extract_field(dict_data, ["type", "敏感词类型"], "默认")
    assert result == "政治敏感"  # 应该找到敏感词类型字段

    # 测试对象访问
    class TestObject:
        def __init__(self):
            self.type = "对象类型"
            self.content = "对象内容"

    obj_data = TestObject()
    result = service._safe_extract_field(obj_data, ["type", "敏感词类型"], "默认")
    assert result == "对象类型"

    # 测试默认值
    result = service._safe_extract_field({}, ["不存在的字段"], "默认值")
    assert result == "默认值"

    # 测试None数据
    result = service._safe_extract_field(None, ["任何字段"], "默认值")
    assert result == "默认值"

    print("✅ _safe_extract_field方法测试通过")


if __name__ == "__main__":
    print("敏感词服务数据转换修复测试")
    print("=" * 50)

    try:
        test_convert_to_target_format()
        test_safe_extract_field()

        print("\n🎉 所有测试通过！敏感词服务数据转换修复成功")

    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback

        traceback.print_exc()
        sys.exit(1)
