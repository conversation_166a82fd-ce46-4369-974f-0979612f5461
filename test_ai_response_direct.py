#!/usr/bin/env python3
"""
直接测试AI模型响应的脚本
"""

import sys
import os
import json

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


def test_ai_response_direct():
    """直接测试AI模型响应"""
    print("🔍 直接测试AI模型响应...")
    print("=" * 80)

    try:
        from app.services.ai_model_service import ai_model_service

        # 构建简单测试消息
        system_prompt = """请返回一个简单的JSON: {"test": "success"}"""
        user_content = """请按要求返回JSON"""

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_content},
        ]

        print("1. 测试简单AI模型调用...")
        raw_response = ai_model_service.call_model(messages, "test-simple")

        print(f"   - 响应长度: {len(raw_response)} 字符")
        print(f"   - 原始内容: '{raw_response}'")
        print()

        print("2. 测试JSON清理...")
        cleaned = ai_model_service.clean_json_data(raw_response)
        print(f"   - 清理后长度: {len(cleaned)} 字符")
        print(f"   - 清理后内容: '{cleaned}'")
        print()

        print("3. 测试JSON解析...")
        try:
            parsed = json.loads(cleaned)
            print(f"   ✅ JSON解析成功: {parsed}")
        except json.JSONDecodeError as e:
            print(f"   ❌ JSON解析失败: {e}")
            print(f"   错误位置: 第{e.lineno}行第{e.colno}列")
            if hasattr(e, "pos"):
                print(f"   错误字符位置: {e.pos}")
                if e.pos < len(cleaned):
                    print(f"   错误字符: '{cleaned[e.pos]}'")
                    print(f"   错误前后文: '{cleaned[max(0, e.pos-10):e.pos+10]}'")

        print()
        print("4. 测试复杂合规性检查...")

        # 构建合规性检查测试消息
        system_prompt2 = """# Role: 招标文件审查与风险管控专家

## OutputFormat
请严格按照以下JSON格式返回审查结果，不要包含其他文本：

{
"checkResultArr": [
    {
    "quesType": "问题类型",
    "quesDesc": "问题描述",
    "originalArr": ["原文内容"],
    "point": "质量控制要点",
    "advice": "处理意见"
    }
]
}

要求：
- 使用中文回答
- 以JSON格式返回结果
- 只返回JSON对象，不要包含其他文本
- 未发现问题则返回空数组
"""

        user_content2 = """请对以下内容进行合规性审查：

项目名称：测试项目
项目编号：TEST001

请按照指定的JSON格式返回结果。"""

        messages2 = [
            {"role": "system", "content": system_prompt2},
            {"role": "user", "content": user_content2},
        ]

        raw_response2 = ai_model_service.call_model(messages2, "test-compliance")

        print(f"   - 响应长度: {len(raw_response2)} 字符")
        print(f"   - 原始内容: '{raw_response2}'")
        print()

        print("5. 测试复杂JSON清理...")
        cleaned2 = ai_model_service.clean_json_data(raw_response2)
        print(f"   - 清理后长度: {len(cleaned2)} 字符")
        print(f"   - 清理后内容: '{cleaned2}'")
        print()

        print("6. 测试复杂JSON解析...")
        try:
            parsed2 = json.loads(cleaned2)
            print(f"   ✅ JSON解析成功: {parsed2}")
        except json.JSONDecodeError as e:
            print(f"   ❌ JSON解析失败: {e}")
            print(f"   错误位置: 第{e.lineno}行第{e.colno}列")
            if hasattr(e, "pos"):
                print(f"   错误字符位置: {e.pos}")
                if e.pos < len(cleaned2):
                    print(f"   错误字符: '{cleaned2[e.pos]}'")
                    print(f"   错误前后文: '{cleaned2[max(0, e.pos-10):e.pos+10]}'")

        return True

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback

        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("🚀 开始AI模型响应直接测试...")

    success = test_ai_response_direct()

    print("\n" + "=" * 80)
    print("📊 测试总结")
    print("=" * 80)

    if success:
        print("🎉 测试完成！")
    else:
        print("❌ 测试失败")
