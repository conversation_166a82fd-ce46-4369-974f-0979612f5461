#!/usr/bin/env python3
"""
详细调试API调用流程的脚本
"""

import sys
import os
import json
import requests
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 设置详细的日志
logging.basicConfig(
    level=logging.DEBUG,
    format="%(asctime)s | %(levelname)s | %(message)s",
)


def debug_api_flow_detailed():
    """详细调试API调用流程"""
    print("🔍 开始详细调试API调用流程...")
    print("=" * 80)

    # 测试文件URL
    file_url = "http://localhost:9090/api/v1/download-shared-object/aHR0cDovLzEyNy4wLjAuMTo5MDAwL3poYW9iaWFvd2VuamlhbmppYW5jaGF6aHVzaG91LyVFNSU4RCU5NyVFNSU4QyVCQSVFNiVCMSVBMSVFNiVCMCVCNCVFNSU4QyVCQiVFNSVCQSU5RiVFMyU4MCU4MSVFNyU5NCU5RiVFNiVCNCVCQiVFNSU5RSU4MyVFNSU5QyVCRSVFNCVCOCU4MCVFNCVCRCU5MyVFNSU4QyU5NiVFNiU4OSU5OCVFNyVBRSVBMSVFNiU5QyU4RCVFNSU4QSVBMSVFOSVBMSVCOSVFNyU5QiVBRSVFNCVCQSU4QyVFNiVBQyVBMSVFNiU4QiU5QiVFNiVBMCU4NyVFNiU5NiU4NyVFNCVCQiVCNiVFRiVCQyU4OCVFNSU4RiU5MSVFNSU5NCVBRSVFNyU4OSU4OCVFRiVCQyU4OS5kb2N4P1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9RVpTNjlMMTlFNjhDS1dSSzVKMTQlMkYyMDI1MDgwOCUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA4MDhUMDEyMDA0WiZYLUFtei1FeHBpcmVzPTQzMTk4JlgtQW16LVNlY3VyaXR5LVRva2VuPWV5SmhiR2NpT2lKSVV6VXhNaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpoWTJObGMzTkxaWGtpT2lKRldsTTJPVXd4T1VVMk9FTkxWMUpMTlVveE5DSXNJbVY0Y0NJNk1UYzFORFkxT1RBek15d2ljR0Z5Wlc1MElqb2ljR1Z1WjJOb1lXNW5JbjAuQXBLbDZqTHVDVmZlWnJiazJpUjBEYmw5d0xCUWVzVURGTnRRUjB3cWg0UjllaGc0bFc4MmlKcnJPb2RLaGdiUTBUY1c5N1hjeE11MHhMMTV6clpRTGcmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JnZlcnNpb25JZD1udWxsJlgtQW16LVNpZ25hdHVyZT00ZDUyMTFhYzE0ZWM5ZmMyYzdkZDMxNGQzNGVhOGRlYzhlZmY5MTJiZmNkNTQzYzM1NDBiNzIyM2MxY2UxZGRk"

    try:
        # 1. 测试文件信息推断
        print("\n1. 测试文件信息推断...")
        try:
            from app.utils.file_info_utils import infer_file_info_from_url

            file_info = infer_file_info_from_url(file_url)
            print(f"✅ 文件信息推断成功:")
            print(f"   - 文件名: {file_info.filename}")
            print(f"   - 扩展名: {file_info.extension}")
            print(f"   - MIME类型: {file_info.mime_type}")
            print(f"   - 文件大小: {file_info.size} 字节")
            print(f"   - URL: {file_info.url[:100]}...")

        except Exception as e:
            print(f"❌ 文件信息推断失败: {e}")
            return False

        # 2. 测试合规性检查请求构建
        print("\n2. 测试合规性检查请求构建...")
        try:
            from app.models.schemas import ComplianceCheckRequest
            from app.models.enums import (
                ProcurementProjectType,
                ProjectCategory,
                BiddingProcurementMethod,
            )

            full_request = ComplianceCheckRequest(
                bidding_doc=file_info,
                procurement_project_type=ProcurementProjectType.SERVICE,
                project_category=ProjectCategory.GOVERNMENT_PROCUREMENT,
                bidding_procurement_method=BiddingProcurementMethod.PUBLIC_BIDDING,
            )

            print(f"✅ 合规性检查请求构建成功:")
            print(f"   - 采购项目类型: {full_request.procurement_project_type}")
            print(f"   - 项目类别: {full_request.project_category}")
            print(f"   - 招标采购方式: {full_request.bidding_procurement_method}")

        except Exception as e:
            print(f"❌ 合规性检查请求构建失败: {e}")
            return False

        # 3. 测试参数验证
        print("\n3. 测试参数验证...")
        try:
            from app.core.validators import ParameterValidator

            validated_request = ParameterValidator.validate_compliance_request(
                full_request.dict()
            )

            print(f"✅ 参数验证成功")
            print(f"   - 验证后的请求类型: {type(validated_request)}")

        except Exception as e:
            print(f"❌ 参数验证失败: {e}")
            return False

        # 4. 测试合规性检查服务
        print("\n4. 测试合规性检查服务...")
        try:
            from app.services.compliance_service import compliance_service

            print("   调用合规性检查服务...")
            result = compliance_service.check_compliance(
                validated_request, "debug-api-flow"
            )

            print(f"✅ 合规性检查服务调用成功:")
            print(f"   - 敏感词数量: {len(result.data.sensitiveWordsArr)}")
            print(f"   - 检查结果数量: {len(result.data.checkResultArr)}")

            if len(result.data.checkResultArr) > 0:
                print("   - 发现的问题:")
                for i, item in enumerate(result.data.checkResultArr[:3]):
                    print(f"     {i+1}. {item.quesType}: {item.quesDesc[:80]}...")
                return True
            else:
                print("   ❌ 未发现任何问题！这是问题所在！")

                # 深入调试合规性检查服务
                print("\n   🔍 深入调试合规性检查服务...")

                # 检查服务健康状态
                health_status = compliance_service.validate_prerequisites(
                    "debug-health"
                )
                print(f"   - 服务健康状态: {health_status}")

                return False

        except Exception as e:
            print(f"❌ 合规性检查服务调用失败: {e}")
            import traceback

            traceback.print_exc()
            return False

    except Exception as e:
        print(f"❌ 调试过程失败: {e}")
        import traceback

        traceback.print_exc()
        return False


def debug_compliance_service_pipeline():
    """调试合规性检查服务的流水线"""
    print("\n" + "=" * 80)
    print("🔍 调试合规性检查服务流水线...")

    try:
        from app.services.compliance_service import compliance_service
        from app.utils.file_info_utils import infer_file_info_from_url
        from app.models.schemas import ComplianceCheckRequest, ProjectInfo
        from app.models.enums import (
            ProcurementProjectType,
            ProjectCategory,
            BiddingProcurementMethod,
        )

        # 文件URL
        file_url = "http://localhost:9090/api/v1/download-shared-object/aHR0cDovLzEyNy4wLjAuMTo5MDAwL3poYW9iaWFvd2VuamlhbmppYW5jaGF6aHVzaG91LyVFNSU4RCU5NyVFNSU4QyVCQSVFNiVCMSVBMSVFNiVCMCVCNCVFNSU4QyVCQiVFNSVCQSU5RiVFMyU4MCU4MSVFNyU5NCU5RiVFNiVCNCVCQiVFNSU5RSU4MyVFNSU5QyVCRSVFNCVCOCU4MCVFNCVCRCU5MyVFNSU4QyU5NiVFNiU4OSU5OCVFNyVBRSVBMSVFNiU5QyU4RCVFNSU4QSVBMSVFOSVBMSVCOSVFNyU5QiVBRSVFNCVCQSU4QyVFNiVBQyVBMSVFNiU4QiU5QiVFNiVBMCU4NyVFNiU5NiU4NyVFNCVCQiVCNiVFRiVCQyU4OCVFNSU4RiU5MSVFNSU5NCVBRSVFNyU4OSU4OCVFRiVCQyU4OS5kb2N4P1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9RVpTNjlMMTlFNjhDS1dSSzVKMTQlMkYyMDI1MDgwOCUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA4MDhUMDEyMDA0WiZYLUFtei1FeHBpcmVzPTQzMTk4JlgtQW16LVNlY3VyaXR5LVRva2VuPWV5SmhiR2NpT2lKSVV6VXhNaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpoWTJObGMzTkxaWGtpT2lKRldsTTJPVXd4T1VVMk9FTkxWMUpMTlVveE5DSXNJbVY0Y0NJNk1UYzFORFkxT1RBek15d2ljR0Z5Wlc1MElqb2ljR1Z1WjJOb1lXNW5JbjAuQXBLbDZqTHVDVmZlWnJiazJpUjBEYmw5d0xCUWVzVURGTnRRUjB3cWg0UjllaGc0bFc4MmlKcnJPb2RLaGdiUTBUY1c5N1hjeE11MHhMMTV6clpRTGcmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JnZlcnNpb25JZD1udWxsJlgtQW16LVNpZ25hdHVyZT00ZDUyMTFhYzE0ZWM5ZmMyYzdkZDMxNGQzNGVhOGRlYzhlZmY5MTJiZmNkNTQzYzM1NDBiNzIyM2MxY2UxZGRk"

        # 构建请求
        file_info = infer_file_info_from_url(file_url)
        request = ComplianceCheckRequest(
            bidding_doc=file_info,
            procurement_project_type=ProcurementProjectType.SERVICE,
            project_category=ProjectCategory.GOVERNMENT_PROCUREMENT,
            bidding_procurement_method=BiddingProcurementMethod.PUBLIC_BIDDING,
        )

        # 获取流水线实例
        pipeline = compliance_service.pipeline

        print("1. 测试文件处理阶段...")
        try:
            content = pipeline.process_file_stage(file_info, "debug-pipeline-file")
            print(f"   ✅ 文件处理成功，内容长度: {len(content)} 字符")
            print(f"   内容预览: {content[:200]}...")
        except Exception as e:
            print(f"   ❌ 文件处理失败: {e}")
            return False

        print("\n2. 测试敏感词检查阶段...")
        try:
            project_info = ProjectInfo(
                procurement_project_type=request.procurement_project_type,
                project_category=request.project_category,
                bidding_procurement_method=request.bidding_procurement_method,
            )

            sensitive_words = pipeline.sensitive_word_check_stage(
                content, project_info, "debug-pipeline-sensitive"
            )
            print(f"   ✅ 敏感词检查成功，发现: {len(sensitive_words)} 个敏感词")
        except Exception as e:
            print(f"   ❌ 敏感词检查失败: {e}")
            return False

        print("\n3. 测试AI合规性检查阶段...")
        try:
            project_info = ProjectInfo(
                procurement_project_type=request.procurement_project_type,
                project_category=request.project_category,
                bidding_procurement_method=request.bidding_procurement_method,
            )

            check_results = pipeline.ai_compliance_check_stage(
                content, project_info, "debug-pipeline-ai"
            )
            print(f"   ✅ AI合规性检查成功，发现: {len(check_results)} 个问题")

            if len(check_results) > 0:
                print("   发现的问题:")
                for i, item in enumerate(check_results[:3]):
                    print(f"     {i+1}. {item.quesType}: {item.quesDesc[:80]}...")
            else:
                print("   ❌ 未发现任何问题！这里可能有问题！")

        except Exception as e:
            print(f"   ❌ AI合规性检查失败: {e}")
            import traceback

            traceback.print_exc()
            return False

        print("\n4. 测试结果处理阶段...")
        try:
            final_result = pipeline.result_processing_stage(
                sensitive_words, check_results, "debug-pipeline-result"
            )
            print(f"   ✅ 结果处理成功")
            print(f"   - 最终敏感词数量: {len(final_result.sensitiveWordsArr)}")
            print(f"   - 最终检查结果数量: {len(final_result.checkResultArr)}")

        except Exception as e:
            print(f"   ❌ 结果处理失败: {e}")
            return False

        return True

    except Exception as e:
        print(f"❌ 流水线调试失败: {e}")
        import traceback

        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("🚀 开始API流程详细调试...")

    success1 = debug_api_flow_detailed()
    success2 = debug_compliance_service_pipeline()

    print("\n" + "=" * 80)
    print("📊 调试总结")
    print("=" * 80)

    if success1 and success2:
        print("🎉 所有测试通过！")
    elif success1:
        print("⚠️ API流程测试通过，但流水线测试失败")
    elif success2:
        print("⚠️ 流水线测试通过，但API流程测试失败")
    else:
        print("❌ 所有测试都失败，需要进一步调试")
