#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试重试逻辑
"""

import sys
import os

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.ai_model_service import AIModelService, AIModelError


def debug_should_retry():
    """调试重试判断逻辑"""
    print("调试重试判断逻辑...")

    service = AIModelService()

    # 创建测试错误
    empty_response_error = AIModelError("空响应", error_code="EMPTY_RESPONSE")

    print(f"错误对象: {empty_response_error}")
    print(f"错误代码: {getattr(empty_response_error, 'error_code', 'None')}")
    print(f"错误消息: {str(empty_response_error)}")

    # 测试重试判断
    result = service._should_retry(empty_response_error, 1, 3)
    print(f"重试判断结果: {result}")

    # 手动检查逻辑
    error_code = getattr(empty_response_error, "error_code", "")
    print(f"获取的错误代码: '{error_code}'")
    print(f"错误代码小写: '{error_code.lower()}'")

    retryable_errors = [
        "EMPTY_RESPONSE",
        "timeout",
        "connection_error",
        "rate_limit",
        "server_error",
        "503",
        "502",
        "500",
        "429",
    ]

    print("检查可重试错误:")
    for retryable in retryable_errors:
        in_code = retryable in error_code.lower()
        in_message = retryable in str(empty_response_error).lower()
        print(f"  {retryable}: 在错误代码中={in_code}, 在错误消息中={in_message}")


if __name__ == "__main__":
    debug_should_retry()
