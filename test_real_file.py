#!/usr/bin/env python3
"""
测试真实文件的合规性检查
"""

import sys
import os
import json
import requests
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 设置详细的日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s | %(levelname)s | %(name)s:%(funcName)s:%(lineno)d | %(message)s",
)


def test_real_file_compliance():
    """测试真实文件的合规性检查"""
    print("开始测试真实文件的合规性检查...")

    # 测试参数
    test_data = {
        "procurement_project_type": "服务类",
        "project_category": "政府采购",
        "bidding_procurement_method": "公开招标",
        "file_url": "http://localhost:9090/api/v1/download-shared-object/aHR0cDovLzEyNy4wLjAuMTo5MDAwL3poYW9iaWFvd2VuamlhbmppYW5jaGF6aHVzaG91LyVFNSU4RCU5NyVFNSU4QyVCQSVFNiVCMSVBMSVFNiVCMCVCNCVFNSU4QyVCQiVFNSVCQSU5RiVFMyU4MCU4MSVFNyU5NCU5RiVFNiVCNCVCQiVFNSU5RSU4MyVFNSU5QyVCRSVFNCVCOCU4MCVFNCVCRCU5MyVFNSU4QyU5NiVFNiU4OSU5OCVFNyVBRSVBMSVFNiU5QyU4RCVFNSU4QSVBMSVFOSVBMSVCOSVFNyU5QiVBRSVFNCVCQSU4QyVFNiVBQyVBMSVFNiU4QiU5QiVFNiVBMCU4NyVFNiU5NiU4NyVFNCVCQiVCNiVFRiVCQyU4OCVFNSU4RiU5MSVFNSU5NCVBRSVFNyU4OSU4OCVFRiVCQyU4OS5kb2N4P1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9RVpTNjlMMTlFNjhDS1dSSzVKMTQlMkYyMDI1MDgwOCUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA4MDhUMDEyMDA0WiZYLUFtei1FeHBpcmVzPTQzMTk4JlgtQW16LVNlY3VyaXR5LVRva2VuPWV5SmhiR2NpT2lKSVV6VXhNaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpoWTJObGMzTkxaWGtpT2lKRldsTTJPVXd4T1VVMk9FTkxWMUpMTlVveE5DSXNJbVY0Y0NJNk1UYzFORFkxT1RBek15d2ljR0Z5Wlc1MElqb2ljR1Z1WjJOb1lXNW5JbjAuQXBLbDZqTHVDVmZlWnJiazJpUjBEYmw5d0xCUWVzVURGTnRRUjB3cWg0UjllaGc0bFc4MmlKcnJPb2RLaGdiUTBUY1c5N1hjeE11MHhMMTV6clpRTGcmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JnZlcnNpb25JZD1udWxsJlgtQW16LVNpZ25hdHVyZT00ZDUyMTFhYzE0ZWM5ZmMyYzdkZDMxNGQzNGVhOGRlYzhlZmY5MTJiZmNkNTQzYzM1NDBiNzIyM2MxY2UxZGRk",
    }

    print(f"测试参数:")
    print(f"- 采购项目类型: {test_data['procurement_project_type']}")
    print(f"- 项目类别: {test_data['project_category']}")
    print(f"- 招标采购方式: {test_data['bidding_procurement_method']}")
    print(f"- 文件URL: {test_data['file_url'][:100]}...")

    try:
        # 发送请求到合规性检查API
        api_url = "http://localhost:8088/api/v1/check-compliance-simple"

        print(f"\n=== 发送请求到API ===")
        print(f"API地址: {api_url}")

        headers = {"Content-Type": "application/json", "User-Agent": "TestScript/1.0.0"}

        # 发送POST请求
        response = requests.post(
            api_url, json=test_data, headers=headers, timeout=120  # 2分钟超时
        )

        print(f"响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")

        if response.status_code == 200:
            print("✅ 请求成功!")

            # 解析响应
            try:
                result = response.json()
                print(f"\n=== 响应结果 ===")
                print(f"响应类型: {type(result)}")

                # 检查sensitiveWordsArr
                if "sensitiveWordsArr" in result:
                    sensitive_words = result["sensitiveWordsArr"]
                    print(f"敏感词数量: {len(sensitive_words)}")

                    if len(sensitive_words) > 0:
                        print("前5个敏感词:")
                        for i, word in enumerate(sensitive_words[:5]):
                            print(
                                f"  {i+1}. {word.get('content', 'N/A')} - {word.get('type', 'N/A')} (出现{word.get('num', 0)}次)"
                            )
                    else:
                        print("未发现敏感词")
                else:
                    print("❌ 响应中缺少sensitiveWordsArr字段")

                # 检查checkResultArr
                if "checkResultArr" in result:
                    check_results = result["checkResultArr"]
                    print(f"合规性检查结果数量: {len(check_results)}")

                    if len(check_results) > 0:
                        print("合规性检查问题:")
                        for i, item in enumerate(check_results):
                            print(f"  问题 {i+1}:")
                            print(f"    类型: {item.get('quesType', 'N/A')}")
                            print(f"    描述: {item.get('quesDesc', 'N/A')[:100]}...")
                            print(f"    要点: {item.get('point', 'N/A')}")
                            print(f"    建议: {item.get('advice', 'N/A')[:100]}...")
                    else:
                        print("❌ 未发现合规性问题 - 这可能表明存在问题!")
                        print("可能的原因:")
                        print("1. 文档内容确实没有合规性问题")
                        print("2. AI模型无法识别该文档中的问题")
                        print("3. 文档内容过于简单或不完整")
                else:
                    print("❌ 响应中缺少checkResultArr字段")

                # 打印完整响应（截断）
                print(f"\n=== 完整响应（前1000字符）===")
                response_str = json.dumps(result, ensure_ascii=False, indent=2)
                print(response_str[:1000])
                if len(response_str) > 1000:
                    print("...(响应已截断)")

            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {str(e)}")
                print(f"原始响应内容: {response.text[:500]}...")

        else:
            print(f"❌ 请求失败!")
            print(f"错误信息: {response.text}")

    except requests.exceptions.Timeout:
        print("❌ 请求超时!")
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败! 请确保API服务正在运行")
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    test_real_file_compliance()
