#!/usr/bin/env python3
"""
调试AI模型原始响应的脚本
"""

import sys
import os
import json
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 设置详细的日志
logging.basicConfig(
    level=logging.DEBUG,
    format="%(asctime)s | %(levelname)s | %(message)s",
)


def debug_ai_raw_response():
    """调试AI模型的原始响应"""
    print("🔍 开始调试AI模型原始响应...")
    print("=" * 80)

    try:
        from app.services.ai_model_service import ai_model_service
        from app.models.enums import (
            ProcurementProjectType,
            ProjectCategory,
            BiddingProcurementMethod,
        )

        # 构建简单测试消息
        system_prompt = """请返回一个简单的JSON: {"test": "success"}"""
        user_content = """请按要求返回JSON"""

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_content},
        ]

        print("1. 测试简单AI模型调用...")
        raw_response = ai_model_service.call_model(messages, "debug-simple")

        print(f"   - 响应长度: {len(raw_response)} 字符")
        print(f"   - 原始内容: '{raw_response}'")
        print()

        # 构建合规性检查测试消息
        system_prompt2 = """# Role: 招标文件审查与风险管控专家

## OutputFormat
请严格按照以下JSON格式返回审查结果，不要包含其他文本：

{
"checkResultArr": [
    {
    "quesType": "问题类型",
    "quesDesc": "问题描述",
    "originalArr": ["原文内容"],
    "point": "质量控制要点",
    "advice": "处理意见"
    }
]
}

要求：
- 使用中文回答
- 以JSON格式返回结果
- 只返回JSON对象，不要包含其他文本
- 未发现问题则返回空数组
"""

        user_content2 = """请对以下内容进行合规性审查：

项目名称：测试项目
项目编号：TEST001

请按照指定的JSON格式返回结果。"""

        messages2 = [
            {"role": "system", "content": system_prompt2},
            {"role": "user", "content": user_content2},
        ]

        print("2. 测试合规性检查AI模型调用...")
        raw_response2 = ai_model_service.call_model(messages2, "debug-compliance")

        print(f"   - 响应长度: {len(raw_response2)} 字符")
        print(f"   - 原始内容: '{raw_response2}'")
        print()

        print("3. 尝试JSON解析...")
        try:
            parsed_json = json.loads(raw_response2)
            print(f"   ✅ JSON解析成功: {parsed_json}")
        except json.JSONDecodeError as e:
            print(f"   ❌ JSON解析失败: {e}")

        return True

    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback

        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("🚀 开始AI模型原始响应调试...")

    success = debug_ai_raw_response()

    print("\n" + "=" * 80)
    print("📊 调试总结")
    print("=" * 80)

    if success:
        print("🎉 调试完成！")
    else:
        print("❌ 调试失败，需要进一步检查")
