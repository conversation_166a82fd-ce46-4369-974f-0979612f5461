#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试直接验证端点
"""

import requests
import json
import time


def test_direct_validation():
    """测试直接验证端点"""
    print("测试直接验证端点")
    print("=" * 50)

    base_url = "http://localhost:8088"

    # 测试数据
    file_info_data = {
        "filename": "test.docx",
        "extension": ".docx",
        "mime_type": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "size": 1024,
        "url": "http://example.com/test.docx",
    }

    print("发送直接验证请求...")
    print(f"请求数据: {json.dumps(file_info_data, indent=2)}")

    try:
        start_time = time.time()

        response = requests.post(
            f"{base_url}/api/v1/debug/validate-file-direct",
            json=file_info_data,
            timeout=30,
        )

        elapsed = time.time() - start_time
        print(f"响应时间: {elapsed:.2f}秒")
        print(f"状态码: {response.status_code}")

        if response.status_code == 200:
            result = response.json()
            print("✅ 直接验证成功")
            print(f"响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            return True
        else:
            print(f"❌ 直接验证失败")
            print(f"响应: {response.text}")
            return False

    except requests.exceptions.Timeout:
        elapsed = time.time() - start_time
        print(f"❌ 直接验证超时，耗时: {elapsed:.2f}秒")
        return False
    except Exception as e:
        elapsed = time.time() - start_time
        print(f"❌ 直接验证异常，耗时: {elapsed:.2f}秒，错误: {str(e)}")
        return False


def test_original_validation():
    """测试原始验证端点"""
    print("\n测试原始验证端点")
    print("=" * 50)

    base_url = "http://localhost:8088"

    # 测试数据
    file_info_data = {
        "filename": "test.docx",
        "extension": ".docx",
        "mime_type": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "size": 1024,
        "url": "http://example.com/test.docx",
    }

    print("发送原始验证请求...")

    try:
        start_time = time.time()

        response = requests.post(
            f"{base_url}/api/v1/validate-file",
            json=file_info_data,
            timeout=10,  # 短超时
        )

        elapsed = time.time() - start_time
        print(f"响应时间: {elapsed:.2f}秒")
        print(f"状态码: {response.status_code}")

        if response.status_code == 200:
            result = response.json()
            print("✅ 原始验证成功")
            print(f"响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            return True
        else:
            print(f"❌ 原始验证失败")
            print(f"响应: {response.text}")
            return False

    except requests.exceptions.Timeout:
        elapsed = time.time() - start_time
        print(f"❌ 原始验证超时，耗时: {elapsed:.2f}秒")
        return False
    except Exception as e:
        elapsed = time.time() - start_time
        print(f"❌ 原始验证异常，耗时: {elapsed:.2f}秒，错误: {str(e)}")
        return False


def test_step_by_step():
    """逐步测试文件处理器组件"""
    print("\n逐步测试文件处理器组件")
    print("=" * 50)

    try:
        # 直接导入和测试
        import sys

        sys.path.append(".")

        from app.models.schemas import FileInfo
        from app.models.enums import FileExtension, MimeType
        from app.services.file_processor_v2 import optimized_file_processor

        print("1. 创建FileInfo对象...")
        start_time = time.time()

        file_info = FileInfo(
            filename="test.docx",
            extension=FileExtension.DOCX,
            mime_type=MimeType.DOCX,
            size=1024,
            url="http://example.com/test.docx",
        )

        elapsed = time.time() - start_time
        print(f"   ✅ FileInfo创建完成，耗时: {elapsed:.3f}秒")

        print("2. 测试文件格式验证...")
        start_time = time.time()

        try:
            optimized_file_processor.validate_file_format(file_info)
            elapsed = time.time() - start_time
            print(f"   ✅ 文件格式验证完成，耗时: {elapsed:.3f}秒")
        except Exception as e:
            elapsed = time.time() - start_time
            print(f"   ❌ 文件格式验证失败，耗时: {elapsed:.3f}秒，错误: {str(e)}")
            return False

        print("3. 测试处理能力验证...")
        start_time = time.time()

        try:
            capability = optimized_file_processor.validate_processing_capability(
                file_info
            )
            elapsed = time.time() - start_time
            print(f"   ✅ 处理能力验证完成，耗时: {elapsed:.3f}秒")
            print(f"   结果: {json.dumps(capability, indent=4, ensure_ascii=False)}")
            return True
        except Exception as e:
            elapsed = time.time() - start_time
            print(f"   ❌ 处理能力验证失败，耗时: {elapsed:.3f}秒，错误: {str(e)}")
            return False

    except ImportError as e:
        print(f"❌ 导入失败: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ 逐步测试失败: {str(e)}")
        return False


def main():
    """主函数"""
    print("直接验证端点测试")
    print("=" * 60)

    # 重启服务提示
    print("请确保服务已重启以包含新的调试端点")
    print("=" * 60)

    tests = [
        ("逐步组件测试", test_step_by_step),
        ("直接验证端点", test_direct_validation),
        ("原始验证端点", test_original_validation),
    ]

    results = {}

    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"执行: {test_name}")
        print("=" * 60)

        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ {test_name} 异常: {str(e)}")
            results[test_name] = False

    # 分析结果
    print(f"\n{'='*60}")
    print("问题分析")
    print("=" * 60)

    if results.get("逐步组件测试", False):
        print("✅ 文件处理器组件本身正常")
    else:
        print("❌ 文件处理器组件有问题")

    if results.get("直接验证端点", False):
        print("✅ 直接验证端点正常")
        if not results.get("原始验证端点", False):
            print("🔍 问题在于中间件或API装饰器")
    else:
        print("❌ 直接验证端点也有问题")
        print("🔍 问题在于更深层的组件或依赖")

    print(f"\n{'='*60}")
    print("建议的下一步")
    print("=" * 60)

    if results.get("逐步组件测试", False) and not results.get("直接验证端点", False):
        print("1. 检查FastAPI的异步处理")
        print("2. 检查Pydantic模型验证")
        print("3. 检查路由装饰器")
    elif not results.get("逐步组件测试", False):
        print("1. 检查文件处理器的依赖导入")
        print("2. 检查日志配置")
        print("3. 检查模型定义")


if __name__ == "__main__":
    main()
