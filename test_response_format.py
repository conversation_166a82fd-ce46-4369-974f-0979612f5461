#!/usr/bin/env python3
"""
测试新的响应格式是否正确
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.models.schemas import ComplianceCheckResponse, ComplianceCheckData, SensitiveWordItem, CheckResultItem
from app.models.enums import QuestionType

def test_response_format():
    """测试响应格式"""
    print("🔍 测试新的响应格式...")
    
    # 创建测试数据
    sensitive_words = [
        SensitiveWordItem(type="错漏词汇", content="测试词", num=1)
    ]
    
    check_results = [
        CheckResultItem(
            quesType=QuestionType.COMPLIANCE,
            quesDesc="测试问题描述",
            originalArr=["测试原文"],
            point="测试要点",
            advice="测试建议"
        )
    ]
    
    # 创建新格式的响应
    response = ComplianceCheckResponse(
        code=200,
        message="测试成功",
        data=ComplianceCheckData(
            sensitiveWordsArr=sensitive_words,
            checkResultArr=check_results
        )
    )
    
    print(f"✅ 响应创建成功")
    print(f"   code: {response.code}")
    print(f"   message: {response.message}")
    print(f"   敏感词数量: {len(response.data.sensitiveWordsArr)}")
    print(f"   检查结果数量: {len(response.data.checkResultArr)}")
    
    # 测试JSON序列化
    try:
        json_data = response.dict()
        print(f"✅ JSON序列化成功")
        print(f"   JSON结构: {list(json_data.keys())}")
        print(f"   data结构: {list(json_data['data'].keys())}")
    except Exception as e:
        print(f"❌ JSON序列化失败: {e}")
        return False
    
    return True

def test_ai_model_service():
    """测试AI模型服务的响应格式"""
    print("\n🔍 测试AI模型服务...")
    
    try:
        from app.services.ai_model_service import AIModelService
        ai_service = AIModelService()
        
        # 测试空响应创建
        empty_response = ComplianceCheckResponse(
            code=200,
            message="AI检查完成，未发现问题",
            data=ComplianceCheckData(checkResultArr=[])
        )
        
        print(f"✅ AI模型服务空响应创建成功")
        print(f"   检查结果数量: {len(empty_response.data.checkResultArr)}")
        
        return True
    except Exception as e:
        print(f"❌ AI模型服务测试失败: {e}")
        return False

def test_result_processor():
    """测试结果处理器"""
    print("\n🔍 测试结果处理器...")
    
    try:
        from app.services.result_processor import result_processor
        
        # 测试空响应创建
        empty_response = result_processor.create_empty_response("测试原因")
        
        print(f"✅ 结果处理器空响应创建成功")
        print(f"   code: {empty_response.code}")
        print(f"   message: {empty_response.message}")
        print(f"   敏感词数量: {len(empty_response.data.sensitiveWordsArr)}")
        print(f"   检查结果数量: {len(empty_response.data.checkResultArr)}")
        
        return True
    except Exception as e:
        print(f"❌ 结果处理器测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始测试响应格式...")
    
    success = True
    success &= test_response_format()
    success &= test_ai_model_service()
    success &= test_result_processor()
    
    if success:
        print("\n🎉 所有测试通过！响应格式修复成功！")
    else:
        print("\n❌ 部分测试失败，需要进一步修复")
        sys.exit(1)
