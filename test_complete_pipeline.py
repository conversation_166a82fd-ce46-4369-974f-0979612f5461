#!/usr/bin/env python3
"""
完整的端到端测试，验证合规性检查流水线
"""

import sys
import os
import json

sys.path.append(os.path.dirname(os.path.abspath(__file__)))


def test_complete_pipeline():
    """测试完整的合规性检查流水线"""

    print("🔍 测试完整的合规性检查流水线...")

    try:
        from app.services.ai_model_service import ai_model_service
        from app.services.result_processor import result_processor
        from app.models.schemas import (
            CheckResultItem,
            QuestionType,
            ProjectInfo,
            ProcurementProjectType,
            ProjectCategory,
            BiddingProcurementMethod,
        )

        # 1. 模拟AI返回的有问题的JSON响应
        problematic_ai_response = """{
  "checkResultArr": [
    {
      "quesType": "合规性/逻辑性/规范性",
      "quesDesc": "招标文件封面项目名称多出"阿萨德"字样，与招标公告及其他章节的项目名称不一致，存在笔误。",
      "originalArr": [
        "安徽理工大学第一附属医院南区污水医废、生活垃圾一体化托管服务项目二次阿萨德"
      ],
      "point": "项目信息核验，确保文件中的项目名称与招标公告完全一致。",
      "advice": "删除招标文件封面项目名称中多余的"阿萨德"字样，使其与招标公告及其他章节的项目名称保持一致。"
    },
    {
      "quesType": "规范性",
      "quesDesc": "招标文件目录页码与后文实际章节页码不对应，例如"第二章 投标人须知前附表 4"实际页码为8，存在排版错误。",
      "originalArr": [
        "第二章    投标人须知前附表\t4",
        "第三章    投标人须知\t22"
      ],
      "point": "文档格式规范性检查，确保目录页码与实际页码一致。",
      "advice": "重新核对并更新招标文件目录中的页码，确保与实际章节页码一致。"
    }
  ]
}"""

        print(f"模拟AI响应长度: {len(problematic_ai_response)} 字符")
        print("包含问题引号:", '"阿萨德"' in problematic_ai_response)
        print()

        # 2. 测试JSON清理
        print("1. 测试JSON清理...")
        cleaned_response = ai_model_service.clean_json_data(problematic_ai_response)
        print(f"清理后长度: {len(cleaned_response)} 字符")

        # 3. 测试JSON解析
        print("\n2. 测试JSON解析...")
        parsed_data = json.loads(cleaned_response)
        print(f"解析成功，checkResultArr长度: {len(parsed_data['checkResultArr'])}")

        # 4. 测试CheckResultItem创建
        print("\n3. 测试CheckResultItem创建...")
        check_results = []
        for i, item in enumerate(parsed_data["checkResultArr"]):
            ques_type = item.get("quesType", "规范性")
            ques_desc = item.get("quesDesc", "")
            original_arr = item.get("originalArr", [])
            point = item.get("point", "")
            advice = item.get("advice", "")

            # 处理组合类型
            if "/" in ques_type:
                ques_type = ques_type.split("/")[0].strip()

            # 验证问题类型
            valid_types = [e.value for e in QuestionType]
            if ques_type not in valid_types:
                ques_type = QuestionType.STANDARDIZATION.value

            check_result = CheckResultItem(
                quesType=ques_type,
                quesDesc=ques_desc,
                originalArr=original_arr,
                point=point,
                advice=advice,
            )

            check_results.append(check_result)
            print(f"✅ 创建CheckResultItem {i+1}: {check_result.quesType}")

        print(f"成功创建 {len(check_results)} 个CheckResultItem")

        # 5. 测试验证逻辑
        print("\n4. 测试验证逻辑...")
        validated_results = result_processor.validate_check_results(check_results)
        print(f"验证结果: 输入{len(check_results)}个，输出{len(validated_results)}个")

        if len(validated_results) == 0:
            print("❌ 所有结果都被验证逻辑丢弃了！")
            return False

        # 6. 测试结果聚合
        print("\n5. 测试结果聚合...")
        from app.services.result_processor import result_processor

        # 模拟空的敏感词列表
        empty_sensitive_words = []

        aggregated_response = result_processor.aggregate_results_with_integrity_check(
            empty_sensitive_words, validated_results, "test-pipeline"
        )

        print(
            f"聚合结果: 敏感词{len(aggregated_response.data.sensitiveWordsArr)}个，检查结果{len(aggregated_response.data.checkResultArr)}个"
        )

        # 7. 验证最终结果
        print("\n6. 验证最终结果...")
        final_results = aggregated_response.data.checkResultArr

        if len(final_results) == 0:
            print("❌ 最终结果为空！")
            return False

        print("✅ 最终结果验证:")
        for i, result in enumerate(final_results):
            print(f"  结果 {i+1}:")
            print(f"    类型: {result.quesType}")
            print(f"    描述: {result.quesDesc[:50]}...")
            print(f"    要点: {result.point[:50]}...")
            print(f"    建议: {result.advice[:50]}...")
            print(f"    原文: {len(result.originalArr)}个")

        print(f"\n🎉 完整流水线测试成功！最终输出 {len(final_results)} 个检查结果")
        return True

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback

        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_complete_pipeline()
    if success:
        print("\n✅ 完整流水线测试通过，问题已修复！")
    else:
        print("\n❌ 完整流水线测试失败")
    sys.exit(0 if success else 1)
