#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试AI模型重试机制
"""

import sys
import os
import time
from unittest.mock import Mock, patch, MagicMock

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.ai_model_service import AIModelService, AIModelError


def test_should_retry():
    """测试重试判断逻辑"""
    print("测试重试判断逻辑...")

    service = AIModelService()

    # 测试可重试的错误
    retryable_error = AIModelError("连接超时", error_code="timeout")
    assert service._should_retry(retryable_error, 0, 3) == True

    empty_response_error = AIModelError("空响应", error_code="EMPTY_RESPONSE")
    assert service._should_retry(empty_response_error, 1, 3) == True

    rate_limit_error = AIModelError("速率限制", error_code="429")
    assert service._should_retry(rate_limit_error, 2, 3) == True

    # 测试不可重试的错误
    auth_error = AIModelError("认证失败", error_code="401")
    assert service._should_retry(auth_error, 0, 3) == False

    # 测试达到最大重试次数
    assert service._should_retry(retryable_error, 3, 3) == False

    print("✅ 重试判断逻辑测试通过")


def test_execute_model_call():
    """测试单次模型调用"""
    print("\n测试单次模型调用...")

    service = AIModelService()

    # 模拟成功的API调用
    mock_response = Mock()
    mock_response.choices = [Mock()]
    mock_response.choices[0].message.content = "测试响应内容"

    with patch.object(service.client, "chat") as mock_chat:
        mock_chat.completions.create.return_value = mock_response

        messages = [{"role": "user", "content": "测试消息"}]
        result = service._execute_model_call(messages, 30.0, "test-request")

        assert result == "测试响应内容"
        assert mock_chat.completions.create.called

    print("✅ 单次模型调用测试通过")


def test_retry_mechanism():
    """测试重试机制"""
    print("\n测试重试机制...")

    service = AIModelService()

    # 模拟前两次调用失败，第三次成功
    call_count = 0

    def mock_execute_call(messages, timeout, request_id):
        nonlocal call_count
        call_count += 1
        if call_count <= 2:
            raise AIModelError("临时错误", error_code="timeout")
        return "成功响应"

    with patch.object(service, "_execute_model_call", side_effect=mock_execute_call):
        with patch("time.sleep"):  # 跳过实际的延迟等待
            messages = [{"role": "user", "content": "测试消息"}]
            result = service._call_model_with_retry(
                messages, "test-request", max_retries=3, base_delay=0.1
            )

            assert result == "成功响应"
            assert call_count == 3  # 应该调用了3次

    print("✅ 重试机制测试通过")


def test_retry_exhaustion():
    """测试重试耗尽的情况"""
    print("\n测试重试耗尽...")

    service = AIModelService()

    # 模拟所有调用都失败
    def mock_execute_call_fail(messages, timeout, request_id):
        raise AIModelError("持续错误", error_code="timeout")

    with patch.object(
        service, "_execute_model_call", side_effect=mock_execute_call_fail
    ):
        with patch("time.sleep"):  # 跳过实际的延迟等待
            messages = [{"role": "user", "content": "测试消息"}]

            try:
                service._call_model_with_retry(
                    messages, "test-request", max_retries=2, base_delay=0.1
                )
                assert False, "应该抛出异常"
            except AIModelError as e:
                assert "持续错误" in str(e)

    print("✅ 重试耗尽测试通过")


def test_empty_response_retry():
    """测试空响应重试"""
    print("\n测试空响应重试...")

    service = AIModelService()

    # 模拟第一次返回空响应，第二次返回正常响应
    call_count = 0

    def mock_execute_call_empty(messages, timeout, request_id):
        nonlocal call_count
        call_count += 1
        if call_count == 1:
            return ""  # 空响应
        return "正常响应"

    with patch.object(
        service, "_execute_model_call", side_effect=mock_execute_call_empty
    ):
        with patch("time.sleep"):  # 跳过实际的延迟等待
            messages = [{"role": "user", "content": "测试消息"}]
            result = service._call_model_with_retry(
                messages, "test-request", max_retries=2, base_delay=0.1
            )

            assert result == "正常响应"
            assert call_count == 2

    print("✅ 空响应重试测试通过")


if __name__ == "__main__":
    print("AI模型重试机制测试")
    print("=" * 50)

    try:
        test_should_retry()
        test_execute_model_call()
        test_retry_mechanism()
        test_retry_exhaustion()
        test_empty_response_retry()

        print("\n🎉 所有测试通过！AI模型重试机制实现成功")

    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback

        traceback.print_exc()
        sys.exit(1)
