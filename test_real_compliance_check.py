#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实合规性检查测试
"""

import requests
import json
import time
from urllib.parse import urlparse


def get_file_info_from_url(url: str) -> dict:
    """从URL获取文件信息"""
    # 解析URL获取文件名
    parsed_url = urlparse(url)

    # 从URL中提取文件名（这个URL看起来是编码的）
    filename = "南区汽水厂、生活垃圾一体化扫管服务项目二次招标文件（发唱版）.docx"

    return {
        "filename": filename,
        "extension": ".docx",
        "mime_type": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "size": 2 * 1024 * 1024,  # 估算2MB
        "url": url,
    }


def test_real_compliance_check():
    """测试真实的合规性检查"""
    print("真实合规性检查测试")
    print("=" * 60)

    base_url = "http://localhost:8088"
    file_url = "http://localhost:9090/api/v1/download-shared-object/aHR0cDovLzEyNy4wLjAuMTo5MDAwL3poYW9iaWFvd2VuamlhbmppYW5jaGF6aHVzaG91LyVFNSU4RCU5NyVFNSU4QyVCQSVFNiVCMSVBMSVFNiVCMCVCNCVFNSU4QyVCQiVFNSVCQSU5RiVFMyU4MCU4MSVFNyU5NCU5RiVFNiVCNCVCQiVFNSU5RSU4MyVFNSU5QyVCRSVFNCVCOCU4MCVFNCVCRCU5MyVFNSU4QyU5NiVFNiU4OSU5OCVFNyVBRSVBMSVFNiU5QyU4RCVFNSU4QSVBMSVFOSVBMSVCOSVFNyU5QiVBRSVFNCVCQSU4QyVFNiVBQyVBMSVFNiU4QiU5QiVFNiVBMCU4NyVFNiU5NiU4NyVFNCVCQiVCNiVFRiVCQyU4OCVFNSU4RiU5MSVFNSU5NCVBRSVFNyU4OSU4OCVFRiVCQyU4OS5kb2N4P1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9SDNLMVAwMkZBQjJKRUZZSFRUUTAlMkYyMDI1MDgwNiUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA4MDZUMTAwNTU4WiZYLUFtei1FeHBpcmVzPTQzMTk5JlgtQW16LVNlY3VyaXR5LVRva2VuPWV5SmhiR2NpT2lKSVV6VXhNaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpoWTJObGMzTkxaWGtpT2lKSU0wc3hVREF5UmtGQ01rcEZSbGxJVkZSUk1DSXNJbVY0Y0NJNk1UYzFORFV4TnpVMU5Dd2ljR0Z5Wlc1MElqb2ljR1Z1WjJOb1lXNW5JbjAuUGdjMXpiSEZlS3diMkw2cFZ5b2VfVVRlNXVmU3l5UjEySFNhZ1dSWkdfRVo3bFVfcEVTNExlWEZMVkFud0doZEw3NVpENjNmcGFCVl81VGcwcnJvVXcmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JnZlcnNpb25JZD1udWxsJlgtQW16LVNpZ25hdHVyZT1mMzRmNGIzYmJhNjliMzNiZjExZjJmNmFiNWU2ZjYwMmVhNGNjZjc5NzFhOTY5MDkzYTM0MmEwZTQzMmE5MTE3"

    # 获取文件信息
    file_info = get_file_info_from_url(file_url)

    print(f"文件信息:")
    print(f"  文件名: {file_info['filename']}")
    print(f"  扩展名: {file_info['extension']}")
    print(f"  大小: {file_info['size'] / 1024 / 1024:.1f}MB")
    print(f"  URL: {file_url[:100]}...")

    # 构建合规性检查请求
    compliance_request = {
        "procurement_project_type": "服务类",  # 根据文件名判断是服务类项目
        "project_category": "政府采购",
        "bidding_procurement_method": "公开招标",  # 从文件名看是招标项目
        "bidding_doc": file_info,
    }

    print(f"\n项目信息:")
    print(f"  采购项目类型: {compliance_request['procurement_project_type']}")
    print(f"  项目类别: {compliance_request['project_category']}")
    print(f"  招标采购方式: {compliance_request['bidding_procurement_method']}")

    print(f"\n开始合规性检查...")
    print("=" * 60)

    try:
        # 发送合规性检查请求
        start_time = time.time()

        response = requests.post(
            f"{base_url}/api/v1/check-compliance",
            json=compliance_request,
            timeout=300,  # 5分钟超时
        )

        processing_time = time.time() - start_time

        print(f"请求完成，耗时: {processing_time:.2f}秒")
        print(f"状态码: {response.status_code}")

        if response.status_code == 200:
            result = response.json()

            print("\n🎉 合规性检查成功！")
            print("=" * 60)

            # 显示敏感词检测结果
            sensitive_words = result.get("sensitiveWordsArr", [])
            print(f"\n📋 敏感词检测结果: {len(sensitive_words)}个")
            print("-" * 40)

            if sensitive_words:
                for i, word in enumerate(sensitive_words[:10], 1):  # 只显示前10个
                    print(
                        f"{i:2d}. {word.get('type', 'N/A'):12s} | {word.get('content', 'N/A'):20s} | 出现{word.get('num', 0)}次"
                    )

                if len(sensitive_words) > 10:
                    print(f"    ... 还有 {len(sensitive_words) - 10} 个敏感词")
            else:
                print("    ✅ 未发现敏感词")

            # 显示合规性检查结果
            check_results = result.get("checkResultArr", [])
            print(f"\n🔍 合规性检查结果: {len(check_results)}个问题")
            print("-" * 40)

            if check_results:
                for i, check in enumerate(check_results[:10], 1):  # 只显示前10个
                    print(
                        f"{i:2d}. [{check.get('level', 'N/A'):4s}] {check.get('type', 'N/A')}"
                    )
                    print(f"    问题: {check.get('content', 'N/A')}")
                    print(f"    建议: {check.get('suggestion', 'N/A')}")
                    print()

                if len(check_results) > 10:
                    print(f"    ... 还有 {len(check_results) - 10} 个问题")
            else:
                print("    ✅ 未发现合规性问题")

            # 总结
            print("=" * 60)
            print("📊 检查总结")
            print("=" * 60)
            print(f"处理时间: {processing_time:.2f}秒")
            print(f"敏感词数量: {len(sensitive_words)}个")
            print(f"合规性问题: {len(check_results)}个")

            if len(sensitive_words) == 0 and len(check_results) == 0:
                print("🎉 恭喜！该招标文件未发现明显的合规性问题和敏感词！")
            else:
                print("⚠️  发现了一些需要注意的问题，请根据建议进行调整。")

            return True

        else:
            print(f"\n❌ 合规性检查失败")
            print(f"状态码: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False

    except requests.exceptions.Timeout:
        print("\n⏰ 请求超时")
        print("这可能是因为文件较大或网络较慢，请稍后重试")
        return False

    except requests.exceptions.RequestException as e:
        print(f"\n❌ 请求异常: {str(e)}")
        return False

    except Exception as e:
        print(f"\n❌ 处理异常: {str(e)}")
        return False


def main():
    """主函数"""
    print("招标文件合规性检查助手 - 真实文件测试")
    print("=" * 60)

    # 先检查服务状态
    try:
        response = requests.get("http://localhost:8088/health", timeout=5)
        if response.status_code == 200:
            print("✅ 服务状态正常")
        else:
            print("❌ 服务状态异常")
            return 1
    except:
        print("❌ 无法连接到服务，请确保服务已启动")
        return 1

    # 执行真实的合规性检查
    if test_real_compliance_check():
        print("\n🎉 真实文件合规性检查测试完成！")
        return 0
    else:
        print("\n❌ 真实文件合规性检查测试失败")
        return 1


if __name__ == "__main__":
    exit(main())
