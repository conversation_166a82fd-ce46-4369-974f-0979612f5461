#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试完整的 /validate-file 端点
"""

import requests
import json
import time


def test_validate_file_endpoint():
    """测试完整的 /validate-file 端点"""
    print("测试完整的 /validate-file 端点")
    print("=" * 50)

    base_url = "http://localhost:8088"

    # 测试数据
    file_info_data = {
        "filename": "test.docx",
        "extension": ".docx",
        "mime_type": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "size": 1024,
        "url": "http://example.com/test.docx",
    }

    try:
        print("发送请求到 /validate-file...")
        start_time = time.time()
        response = requests.post(
            f"{base_url}/api/v1/validate-file", json=file_info_data, timeout=10
        )
        elapsed = time.time() - start_time

        print(f"响应时间: {elapsed:.2f}秒")
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.text}")

        if response.status_code == 200:
            result = response.json()
            print("✅ /validate-file 端点正常工作")
            print(f"返回结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
            return True
        else:
            print(f"❌ /validate-file 端点失败")
            return False

    except requests.exceptions.Timeout:
        print("❌ /validate-file 端点超时")
        return False
    except Exception as e:
        print(f"❌ /validate-file 端点异常: {str(e)}")
        return False


if __name__ == "__main__":
    test_validate_file_endpoint()
