#!/usr/bin/env python3
"""
测试超时修复效果的脚本
"""

import sys
import os
import time
import requests

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


def test_ai_timeout_fix():
    """测试AI模型超时修复"""
    print("=== 测试AI模型超时修复 ===")

    try:
        from app.services.ai_model_service import AIModelService
        from app.models.schemas import ProjectInfo
        from app.models.enums import (
            ProcurementProjectType,
            ProjectCategory,
            BiddingProcurementMethod,
        )

        ai_service = AIModelService()

        # 创建测试项目信息
        project_info = ProjectInfo(
            procurement_project_type=ProcurementProjectType.GOODS,
            project_category=ProjectCategory.GOVERNMENT_PROCUREMENT,
            bidding_procurement_method=BiddingProcurementMethod.PUBLIC_BIDDING,
        )

        # 创建一个较长的测试内容，模拟真实文件
        test_content = (
            """
        招标公告
        
        一、项目基本情况
        项目编号：TEST-2025-001
        项目名称：测试项目采购
        采购预算：100万元
        
        二、投标人资格要求
        1. 具有独立承担民事责任的能力
        2. 具有良好的商业信誉和健全的财务会计制度
        3. 具有履行合同所必需的设备和专业技术能力
        4. 有依法缴纳税收和社会保障资金的良好记录
        5. 参加政府采购活动前三年内，在经营活动中没有重大违法记录
        
        三、采购需求
        本次采购的货物包括但不限于：
        - 办公设备：电脑、打印机、复印机等
        - 办公家具：办公桌、办公椅、文件柜等
        - 其他办公用品
        
        四、技术规格要求
        1. 所有设备必须符合国家相关标准
        2. 提供原厂质保服务
        3. 具备完善的售后服务体系
        
        五、商务条款
        1. 付款方式：验收合格后30日内付款
        2. 交货期：合同签订后15个工作日内
        3. 交货地点：采购人指定地点
        
        六、评标办法
        本项目采用综合评分法评标，评分标准如下：
        1. 技术部分（40分）
        2. 商务部分（35分）
        3. 价格部分（25分）
        
        七、投标文件要求
        1. 投标文件应包含技术文件、商务文件和价格文件
        2. 投标文件应按照招标文件要求编制
        3. 投标文件应加盖投标人公章
        
        八、开标时间和地点
        开标时间：2025年2月1日上午9:00
        开标地点：采购代理机构会议室
        
        九、联系方式
        采购人：某政府机关
        联系人：张先生
        电话：010-12345678
        
        采购代理机构：某采购代理公司
        联系人：李女士
        电话：010-87654321
        """
            * 3
        )  # 重复3次增加内容长度

        print(f"测试内容长度: {len(test_content)} 字符")
        print("开始AI模型合规性检查（可能需要较长时间）...")

        start_time = time.time()

        try:
            result = ai_service.check_compliance(
                test_content, project_info, "timeout-test"
            )
            end_time = time.time()

            duration = end_time - start_time
            print(f"✅ AI模型调用成功!")
            print(f"调用时间: {duration:.2f}秒")
            print(f"发现问题数量: {len(result.checkResultArr)}")

            if duration > 120:
                print("⚠️  调用时间仍然较长，但在新的超时限制内")
            elif duration > 60:
                print("⚠️  调用时间较长，但可接受")
            else:
                print("✅ 调用时间正常")

            # 显示前几个问题
            if len(result.checkResultArr) > 0:
                print("\n发现的问题（前3个）:")
                for i, item in enumerate(result.checkResultArr[:3]):
                    print(f"  {i+1}. {item.quesType}: {item.quesDesc[:100]}...")

            return True

        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            print(f"❌ AI模型调用失败: {e}")
            print(f"失败前耗时: {duration:.2f}秒")

            if "超时" in str(e) or "timeout" in str(e).lower():
                print("❌ 仍然存在超时问题，可能需要进一步调整")

            return False

    except Exception as e:
        print(f"❌ 测试初始化失败: {e}")
        return False


def test_api_with_real_file():
    """测试API端点处理真实文件"""
    print("\n=== 测试API端点处理真实文件 ===")

    # 使用之前的真实文件URL
    test_data = {
        "procurement_project_type": "服务类",
        "project_category": "政府采购",
        "bidding_procurement_method": "公开招标",
        "file_url": "http://localhost:9090/api/v1/download-shared-object/aHR0cDovLzEyNy4wLjAuMTo5MDAwL3poYW9iaWFvd2VuamlhbmppYW5jaGF6aHVzaG91LyVFNSU4RCU5NyVFNSU4QyVCQSVFNiVCMSVBMSVFNiVCMCVCNCVFNSU4QyVCQiVFNSVCQSU5RiVFMyU4MCU4MSVFNyU5NCU5RiVFNiVCNCVCQiVFNSU5RSU4MyVFNSU5QyVCRSVFNCVCOCU4MCVFNCVCRCU5MyVFNSU4QyU5NiVFNiU4OSU5OCVFNyVBRSVBMSVFNiU5QyU4RCVFNSU4QSVBMSVFOSVBMSVCOSVFNyU5QiVBRSVFNCVCQSU4QyVFNiVBQyVBMSVFNiU4QiU5QiVFNiVBMCU4NyVFNiU5NiU4NyVFNCVCQiVCNiVFRiVCQyU4OCVFNSU4RiU5MSVFNSU5NCVBRSVFNyU4OSU4OCVFRiVCQyU4OS5kb2N4P1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9RVpTNjlMMTlFNjhDS1dSSzVKMTQlMkYyMDI1MDgwOCUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA4MDhUMDEyMDA0WiZYLUFtei1FeHBpcmVzPTQzMTk4JlgtQW16LVNlY3VyaXR5LVRva2VuPWV5SmhiR2NpT2lKSVV6VXhNaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpoWTJObGMzTkxaWGtpT2lKRldsTTJPVXd4T1VVMk9FTkxWMUpMTlVveE5DSXNJbVY0Y0NJNk1UYzFORFkxT1RBek15d2ljR0Z5Wlc1MElqb2ljR1Z1WjJOb1lXNW5JbjAuQXBLbDZqTHVDVmZlWnJiazJpUjBEYmw5d0xCUWVzVURGTnRRUjB3cWg0UjllaGc0bFc4MmlKcnJPb2RLaGdiUTBUY1c5N1hjeE11MHhMMTV6clpRTGcmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JnZlcnNpb25JZD1udWxsJlgtQW16LVNpZ25hdHVyZT00ZDUyMTFhYzE0ZWM5ZmMyYzdkZDMxNGQzNGVhOGRlYzhlZmY5MTJiZmNkNTQzYzM1NDBiNzIyM2MxY2UxZGRk",
    }

    try:
        print("发送请求到API（增加超时时间到180秒）...")
        start_time = time.time()

        response = requests.post(
            "http://localhost:8088/api/v1/check-compliance-simple",
            json=test_data,
            headers={"Content-Type": "application/json"},
            timeout=180,  # 3分钟超时
        )

        end_time = time.time()
        duration = end_time - start_time

        print(f"API响应状态码: {response.status_code}")
        print(f"API响应时间: {duration:.2f}秒")

        if response.status_code == 200:
            result = response.json()
            print("✅ API调用成功!")
            print(f"敏感词数量: {len(result.get('sensitiveWordsArr', []))}")
            print(f"检查结果数量: {len(result.get('checkResultArr', []))}")

            if duration > 120:
                print("⚠️  响应时间较长，但在新的超时限制内")
            elif duration > 60:
                print("⚠️  响应时间中等")
            else:
                print("✅ 响应时间良好")

            # 检查是否有合规性问题
            check_results = result.get("checkResultArr", [])
            if len(check_results) > 0:
                print(f"✅ 发现了 {len(check_results)} 个合规性问题，AI模型工作正常")
                print("前3个问题:")
                for i, item in enumerate(check_results[:3]):
                    print(
                        f"  {i+1}. {item.get('quesType', 'N/A')}: {item.get('quesDesc', 'N/A')[:100]}..."
                    )
            else:
                print("⚠️  未发现合规性问题，可能仍需要调试AI模型响应")

            return True
        else:
            print(f"❌ API调用失败: {response.text}")
            return False

    except requests.exceptions.Timeout:
        print("❌ API请求超时（180秒），可能需要进一步优化")
        return False
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return False


def main():
    """主函数"""
    print("🔧 测试超时修复效果...")
    print("=" * 50)

    tests = [
        ("AI模型超时修复", test_ai_timeout_fix),
        ("API真实文件处理", test_api_with_real_file),
    ]

    results = {}

    for test_name, test_func in tests:
        print(f"🧪 执行测试: {test_name}")
        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            results[test_name] = False
        print()

    # 总结报告
    print("=" * 50)
    print("📊 测试总结报告")
    print("=" * 50)

    passed = sum(1 for r in results.values() if r)
    total = len(results)

    print(f"测试结果: {passed}/{total} 项测试通过")

    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")

    if passed == total:
        print("\n🎉 所有测试都通过了！超时问题已修复。")
    else:
        print(f"\n⚠️  {total - passed} 项测试失败，可能需要进一步调整。")


if __name__ == "__main__":
    main()
