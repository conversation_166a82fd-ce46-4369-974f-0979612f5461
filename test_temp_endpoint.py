#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试临时端点
"""

import requests
import json
import time


def test_temp_endpoints():
    """测试临时端点"""
    print("测试临时调试端点")
    print("=" * 50)

    base_url = "http://localhost:8088"

    # 1. 测试调试信息接口
    print("1. 测试调试信息接口...")
    try:
        response = requests.get(f"{base_url}/api/v1/debug-info", timeout=5)
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        print("✅ 调试接口正常")
    except Exception as e:
        print(f"❌ 调试接口失败: {str(e)}")

    # 2. 测试简化的合规性检查
    print("\n2. 测试简化的合规性检查...")
    request_data = {
        "procurement_project_type": "服务类",
        "project_category": "政府采购",
        "bidding_procurement_method": "公开招标",
        "bidding_doc": {
            "filename": "test.docx",
            "extension": ".docx",
            "mime_type": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "size": 1024,
            "url": "http://example.com/test.docx",
        },
    }

    try:
        start_time = time.time()
        response = requests.post(
            f"{base_url}/api/v1/check-compliance-simple", json=request_data, timeout=10
        )
        processing_time = time.time() - start_time

        print(f"状态码: {response.status_code}")
        print(f"处理时间: {processing_time:.2f}秒")

        if response.status_code == 200:
            result = response.json()
            print("✅ 简化合规性检查成功")
            print(f"敏感词数量: {len(result.get('sensitiveWordsArr', []))}")
            print(f"检查结果数量: {len(result.get('checkResultArr', []))}")
            print(f"响应内容: {json.dumps(result, indent=2, ensure_ascii=False)}")
        else:
            print(f"❌ 简化合规性检查失败: {response.text}")

    except Exception as e:
        print(f"❌ 简化合规性检查异常: {str(e)}")


if __name__ == "__main__":
    test_temp_endpoints()
