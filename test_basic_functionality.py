#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基本功能测试脚本
快速检查系统的核心组件是否可以正常导入和初始化
"""

import sys
import traceback


def test_imports():
    """测试基本导入"""
    print("1. 测试基本导入")
    print("-" * 40)

    try:
        # 测试配置导入
        from app.core.config import settings

        print(f"✅ 配置导入成功")
        print(f"   模型名称: {settings.model_name}")
        print(f"   环境: {settings.environment}")

        # 测试日志导入
        from app.core.logger import log

        print(f"✅ 日志导入成功")
        log.info("测试日志记录")

        # 测试枚举导入
        from app.models.enums import ProcurementProjectType, ProjectCategory

        print(f"✅ 枚举导入成功")
        print(f"   采购项目类型数量: {len(list(ProcurementProjectType))}")

        # 测试数据模型导入
        from app.models.schemas import ComplianceCheckRequest, FileInfo

        print(f"✅ 数据模型导入成功")

        return True

    except Exception as e:
        print(f"❌ 导入失败: {str(e)}")
        print(f"   详细错误: {traceback.format_exc()}")
        return False


def test_service_initialization():
    """测试服务初始化"""
    print("\n2. 测试服务初始化")
    print("-" * 40)

    try:
        # 测试文件处理器
        try:
            from app.services.file_processor_v2 import optimized_file_processor

            stats = optimized_file_processor.get_processing_stats()
            print(f"✅ 文件处理器初始化成功")
            print(f"   MarkItDown可用: {stats.get('markitdown_available', False)}")
        except Exception as e:
            print(f"❌ 文件处理器初始化失败: {str(e)}")

        # 测试AI模型服务
        try:
            from app.services.ai_model_service import ai_model_service

            model_info = ai_model_service.get_model_info()
            print(f"✅ AI模型服务初始化成功")
            print(f"   模型名称: {model_info.get('model_name', 'N/A')}")
            print(f"   客户端初始化: {model_info.get('client_initialized', False)}")
        except Exception as e:
            print(f"❌ AI模型服务初始化失败: {str(e)}")

        # 测试敏感词服务
        try:
            from app.services.sensitive_word_service import sensitive_word_service

            service_info = sensitive_word_service.get_service_info()
            print(f"✅ 敏感词服务初始化成功")
            print(f"   基础URL: {service_info.get('base_url', 'N/A')}")
        except Exception as e:
            print(f"❌ 敏感词服务初始化失败: {str(e)}")

        # 测试结果处理器
        try:
            from app.services.result_processor import result_processor

            stats = result_processor.get_processing_stats()
            print(f"✅ 结果处理器初始化成功")
            print(f"   去重启用: {stats.get('deduplication_enabled', False)}")
        except Exception as e:
            print(f"❌ 结果处理器初始化失败: {str(e)}")

        # 测试合规性服务
        try:
            from app.services.compliance_service import compliance_service

            status = compliance_service.get_service_status()
            print(f"✅ 合规性服务初始化成功")
            print(f"   服务名称: {status['service_info']['service_name']}")
        except Exception as e:
            print(f"❌ 合规性服务初始化失败: {str(e)}")

        return True

    except Exception as e:
        print(f"❌ 服务初始化测试失败: {str(e)}")
        return False


def test_data_model_creation():
    """测试数据模型创建"""
    print("\n3. 测试数据模型创建")
    print("-" * 40)

    try:
        from app.models.schemas import ComplianceCheckRequest, FileInfo
        from app.models.enums import (
            ProcurementProjectType,
            ProjectCategory,
            BiddingProcurementMethod,
            FileExtension,
            MimeType,
        )

        # 创建文件信息
        file_info = FileInfo(
            filename="test_document.docx",
            extension=FileExtension.DOCX,
            mime_type=MimeType.DOCX,
            size=1024 * 1024,  # 1MB
            url="http://example.com/test_document.docx",
        )
        print(f"✅ FileInfo创建成功: {file_info.filename}")

        # 创建合规性检查请求
        request = ComplianceCheckRequest(
            procurement_project_type=ProcurementProjectType.GOODS,
            project_category=ProjectCategory.GOVERNMENT_PROCUREMENT,
            bidding_procurement_method=BiddingProcurementMethod.PUBLIC_BIDDING,
            bidding_doc=file_info,
        )
        print(f"✅ ComplianceCheckRequest创建成功")

        # 测试项目信息提取
        project_info = request.get_project_info()
        print(f"✅ 项目信息提取成功: {project_info.procurement_project_type.value}")

        return True

    except Exception as e:
        print(f"❌ 数据模型创建失败: {str(e)}")
        print(f"   详细错误: {traceback.format_exc()}")
        return False


def test_api_routes_import():
    """测试API路由导入"""
    print("\n4. 测试API路由导入")
    print("-" * 40)

    try:
        from app.api.routes import router

        print(f"✅ API路由导入成功")

        # 检查路由数量
        route_count = len(router.routes)
        print(f"   注册的路由数量: {route_count}")

        # 列出主要路由
        main_routes = []
        for route in router.routes:
            if hasattr(route, "path"):
                main_routes.append(f"{route.methods} {route.path}")

        print(f"   主要路由:")
        for route in main_routes[:5]:  # 显示前5个路由
            print(f"     {route}")

        return True

    except Exception as e:
        print(f"❌ API路由导入失败: {str(e)}")
        print(f"   详细错误: {traceback.format_exc()}")
        return False


def test_main_app_creation():
    """测试主应用创建"""
    print("\n5. 测试主应用创建")
    print("-" * 40)

    try:
        from main import app

        print(f"✅ FastAPI应用创建成功")
        print(f"   应用标题: {app.title}")
        print(f"   应用版本: {app.version}")

        # 检查中间件
        middleware_count = len(app.user_middleware)
        print(f"   中间件数量: {middleware_count}")

        return True

    except Exception as e:
        print(f"❌ 主应用创建失败: {str(e)}")
        print(f"   详细错误: {traceback.format_exc()}")
        return False


def main():
    """主函数"""
    print("招标文件合规性检查助手 - 基本功能测试")
    print("=" * 60)

    tests = [
        ("基本导入", test_imports),
        ("服务初始化", test_service_initialization),
        ("数据模型创建", test_data_model_creation),
        ("API路由导入", test_api_routes_import),
        ("主应用创建", test_main_app_creation),
    ]

    passed = 0
    total = len(tests)

    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ 测试 '{test_name}' 异常: {str(e)}")

    # 显示总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    print(f"总测试数: {total}")
    print(f"通过数: {passed}")
    print(f"失败数: {total - passed}")
    print(f"通过率: {passed / total:.1%}")

    if passed == total:
        print("\n🎉 所有基本功能测试通过！系统可以正常启动。")
        print("建议运行以下命令启动服务:")
        print("  python main.py")
    elif passed >= total * 0.8:
        print("\n✅ 大部分功能正常，系统应该可以启动。")
        print("建议修复失败的测试后再启动服务。")
    else:
        print("\n❌ 系统存在严重问题，请检查配置和依赖。")

    return 0 if passed == total else 1


if __name__ == "__main__":
    exit(main())
