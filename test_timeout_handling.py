#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试服务超时处理
"""

import sys
import os
import time
from unittest.mock import Mock, patch

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.compliance_service import ComplianceCheckPipeline
from app.services.sensitive_word_service import SensitiveWordService
from app.models.schemas import ComplianceCheckRequest, FileInfo, ProjectInfo
from app.models.enums import (
    ProcurementProjectType,
    ProjectCategory,
    BiddingProcurementMethod,
    FileExtension,
    MimeType,
)


def test_pipeline_timeout_check():
    """测试流水线超时检查功能"""
    print("测试流水线超时检查功能...")

    pipeline = ComplianceCheckPipeline()

    # 创建测试请求
    file_info = FileInfo(
        filename="test.docx",
        extension=FileExtension.DOCX,
        mime_type=MimeType.DOCX,
        size=1024,
        url="http://example.com/test.docx",
    )

    request = ComplianceCheckRequest(
        procurement_project_type=ProcurementProjectType.SERVICE,
        project_category=ProjectCategory.GOVERNMENT_PROCUREMENT,
        bidding_procurement_method=BiddingProcurementMethod.PUBLIC_BIDDING,
        bidding_doc=file_info,
    )

    # 模拟一个会超时的处理过程
    def slow_process_file_stage(file_info, request_id):
        time.sleep(2)  # 模拟慢速处理
        return "测试内容"

    with patch.object(
        pipeline, "process_file_stage", side_effect=slow_process_file_stage
    ):
        with patch.object(
            pipeline,
            "validate_prerequisites",
            return_value={
                "file_processor": True,
                "ai_model": True,
                "sensitive_word": True,
                "result_processor": True,
            },
        ):
            try:
                # 设置很短的超时时间（1秒）
                result = pipeline.execute_pipeline(
                    request, "test-timeout", pipeline_timeout=1.0
                )

                # 应该返回超时响应而不是抛出异常
                assert result is not None
                assert len(result.checkResultArr) == 0  # 超时时应该返回空结果
                print("✅ 流水线超时处理测试通过")

            except TimeoutError:
                print("❌ 超时异常未被正确处理")
                raise


def test_sensitive_word_timeout():
    """测试敏感词检测超时"""
    print("\n测试敏感词检测超时...")

    service = SensitiveWordService()

    # 创建项目信息
    project_info = ProjectInfo(
        procurement_project_type=ProcurementProjectType.SERVICE,
        project_category=ProjectCategory.GOVERNMENT_PROCUREMENT,
        bidding_procurement_method=BiddingProcurementMethod.PUBLIC_BIDDING,
    )

    # 模拟超时的HTTP请求
    def mock_post_timeout(*args, **kwargs):
        import requests

        raise requests.exceptions.Timeout("请求超时")

    with patch.object(service.session, "post", side_effect=mock_post_timeout):
        try:
            # 使用带降级机制的检测，应该返回空列表而不是抛出异常
            result = service.detect_with_fallback(
                "测试内容", project_info, "test-timeout", timeout=1.0
            )

            assert isinstance(result, list)
            assert len(result) == 0  # 超时时应该返回空列表
            print("✅ 敏感词检测超时处理测试通过")

        except Exception as e:
            print(f"❌ 敏感词检测超时处理失败: {str(e)}")
            raise


def test_timeout_warning():
    """测试超时警告功能"""
    print("\n测试超时警告功能...")

    pipeline = ComplianceCheckPipeline()

    # 创建测试请求
    file_info = FileInfo(
        filename="test.docx",
        extension=FileExtension.DOCX,
        mime_type=MimeType.DOCX,
        size=1024,
        url="http://example.com/test.docx",
    )

    request = ComplianceCheckRequest(
        procurement_project_type=ProcurementProjectType.SERVICE,
        project_category=ProjectCategory.GOVERNMENT_PROCUREMENT,
        bidding_procurement_method=BiddingProcurementMethod.PUBLIC_BIDDING,
        bidding_doc=file_info,
    )

    # 模拟一个会触发警告但不超时的处理过程
    def medium_speed_process(file_info, request_id):
        time.sleep(0.9)  # 90% of 1 second timeout
        return "测试内容"

    with patch.object(pipeline, "process_file_stage", side_effect=medium_speed_process):
        with patch.object(
            pipeline,
            "validate_prerequisites",
            return_value={
                "file_processor": True,
                "ai_model": False,
                "sensitive_word": False,
                "result_processor": True,
            },
        ):
            with patch.object(pipeline, "ai_compliance_check_stage", return_value=[]):
                with patch.object(
                    pipeline, "sensitive_word_check_stage", return_value=[]
                ):
                    try:
                        # 设置1秒超时，处理时间0.9秒应该触发警告但不超时
                        result = pipeline.execute_pipeline(
                            request, "test-warning", pipeline_timeout=1.0
                        )

                        assert result is not None
                        print("✅ 超时警告功能测试通过")

                    except Exception as e:
                        print(f"❌ 超时警告功能测试失败: {str(e)}")
                        raise


if __name__ == "__main__":
    print("服务超时处理测试")
    print("=" * 50)

    try:
        test_pipeline_timeout_check()
        test_sensitive_word_timeout()
        test_timeout_warning()

        print("\n🎉 所有测试通过！服务超时处理实现成功")

    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback

        traceback.print_exc()
        sys.exit(1)
