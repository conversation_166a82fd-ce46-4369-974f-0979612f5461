# -*- coding: utf-8 -*-
"""
优化后的文件处理服务
直接使用MarkItDown处理文件，避免不必要的中间转换
"""

import os
import tempfile
from io import BytesIO
from typing import Optional, Tuple
import filetype
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

# 可选的MarkItDown库
try:
    from markitdown import MarkItDown

    MARKITDOWN_AVAILABLE = True
except ImportError:
    MarkItDown = None
    MARKITDOWN_AVAILABLE = False

# 备用文档处理库
import docx
import pdfplumber

from app.core.config import settings
from app.core.logger import log, performance_logger, TimingContext, log_function_call
from app.models.enums import FileExtension, MimeType
from app.models.schemas import FileInfo


class FileDownloadError(Exception):
    """文件下载异常"""

    def __init__(
        self,
        message: str,
        url: str = "",
        status_code: int = None,
        file_size: int = None,
    ):
        """
        初始化文件下载异常

        Args:
            message: 错误消息
            url: 下载URL
            status_code: HTTP状态码
            file_size: 文件大小
        """
        super().__init__(message)
        self.url = url
        self.status_code = status_code
        self.file_size = file_size
        self.error_type = "FILE_DOWNLOAD_ERROR"

    def __str__(self):
        base_msg = super().__str__()
        details = []

        if self.url:
            details.append(f"URL: {self.url}")
        if self.status_code:
            details.append(f"状态码: {self.status_code}")
        if self.file_size:
            details.append(f"文件大小: {self.file_size} 字节")

        if details:
            return f"{base_msg} ({', '.join(details)})"
        return base_msg


class FileProcessingError(Exception):
    """文件处理异常"""

    def __init__(
        self,
        message: str,
        filename: str = "",
        file_type: str = "",
        processing_stage: str = "",
        original_error: Exception = None,
    ):
        """
        初始化文件处理异常

        Args:
            message: 错误消息
            filename: 文件名
            file_type: 文件类型
            processing_stage: 处理阶段
            original_error: 原始异常
        """
        super().__init__(message)
        self.filename = filename
        self.file_type = file_type
        self.processing_stage = processing_stage
        self.original_error = original_error
        self.error_type = "FILE_PROCESSING_ERROR"

    def __str__(self):
        base_msg = super().__str__()
        details = []

        if self.filename:
            details.append(f"文件: {self.filename}")
        if self.file_type:
            details.append(f"类型: {self.file_type}")
        if self.processing_stage:
            details.append(f"阶段: {self.processing_stage}")
        if self.original_error:
            details.append(f"原因: {str(self.original_error)}")

        if details:
            return f"{base_msg} ({', '.join(details)})"
        return base_msg


class FileFormatError(Exception):
    """文件格式异常"""

    def __init__(
        self,
        message: str,
        filename: str = "",
        expected_format: str = "",
        actual_format: str = "",
        file_size: int = None,
    ):
        """
        初始化文件格式异常

        Args:
            message: 错误消息
            filename: 文件名
            expected_format: 期望的格式
            actual_format: 实际的格式
            file_size: 文件大小
        """
        super().__init__(message)
        self.filename = filename
        self.expected_format = expected_format
        self.actual_format = actual_format
        self.file_size = file_size
        self.error_type = "FILE_FORMAT_ERROR"

    def __str__(self):
        base_msg = super().__str__()
        details = []

        if self.filename:
            details.append(f"文件: {self.filename}")
        if self.expected_format:
            details.append(f"期望格式: {self.expected_format}")
        if self.actual_format:
            details.append(f"实际格式: {self.actual_format}")
        if self.file_size is not None:
            details.append(f"文件大小: {self.file_size} 字节")

        if details:
            return f"{base_msg} ({', '.join(details)})"
        return base_msg


def create_robust_session() -> requests.Session:
    """
    创建一个具有重试机制和连接池的健壮会话
    """
    session = requests.Session()

    retry_strategy = Retry(
        total=settings.max_retries,
        backoff_factor=1,
        status_forcelist=[429, 500, 502, 503, 504],
        allowed_methods=["HEAD", "GET", "OPTIONS"],
    )

    adapter = HTTPAdapter(
        max_retries=retry_strategy,
        pool_connections=10,
        pool_maxsize=20,
        pool_block=False,
    )

    session.mount("http://", adapter)
    session.mount("https://", adapter)

    return session


class OptimizedFileProcessor:
    """优化后的文件处理器"""

    def __init__(self):
        self.session = create_robust_session()
        self.temp_dir = tempfile.gettempdir()

    @log_function_call
    def validate_file_format(self, file_info: FileInfo) -> bool:
        """验证文件格式"""
        if file_info.extension not in [FileExtension.DOCX, FileExtension.PDF]:
            raise FileFormatError(
                f"不支持的文件扩展名: {file_info.extension}",
                filename=file_info.filename,
                actual_format=file_info.extension.value,
                expected_format="docx或pdf",
            )

        if file_info.mime_type not in [MimeType.DOCX, MimeType.PDF]:
            raise FileFormatError(
                f"不支持的MIME类型: {file_info.mime_type}",
                filename=file_info.filename,
                actual_format=file_info.mime_type.value,
                expected_format="docx或pdf的MIME类型",
            )

        valid_combinations = {
            FileExtension.DOCX: MimeType.DOCX,
            FileExtension.PDF: MimeType.PDF,
        }

        if valid_combinations.get(file_info.extension) != file_info.mime_type:
            raise FileFormatError(
                f"文件扩展名与MIME类型不匹配",
                filename=file_info.filename,
                expected_format=f"{file_info.extension.value} + {valid_combinations.get(file_info.extension).value}",
                actual_format=f"{file_info.extension.value} + {file_info.mime_type.value}",
            )

        if file_info.size <= 0:
            raise FileFormatError(
                "文件大小必须大于0",
                filename=file_info.filename,
                file_size=file_info.size,
            )

        if file_info.size > settings.max_file_size:
            max_size_mb = settings.max_file_size / 1024 / 1024
            raise FileFormatError(
                f"文件大小不能超过 {max_size_mb:.0f}MB",
                filename=file_info.filename,
                file_size=file_info.size,
            )

        return True

    @log_function_call
    def download_file(self, url: str, request_id: str = "") -> bytes:
        """下载文件"""
        try:
            with TimingContext("文件下载", request_id):
                log.info(f"开始下载文件: {url}")

                response = self.session.get(
                    url, timeout=settings.request_timeout, stream=True
                )
                response.raise_for_status()

                content_length = response.headers.get("content-length")
                if content_length:
                    content_length = int(content_length)
                    if content_length > settings.max_file_size:
                        raise FileDownloadError(
                            f"文件大小超过限制: {content_length} 字节",
                            url=url,
                            status_code=response.status_code,
                            file_size=content_length,
                        )

                file_content = BytesIO()
                downloaded_size = 0

                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        file_content.write(chunk)
                        downloaded_size += len(chunk)

                        if downloaded_size > settings.max_file_size:
                            raise FileDownloadError(
                                f"下载大小超过限制: {downloaded_size} 字节",
                                url=url,
                                file_size=downloaded_size,
                            )

                file_bytes = file_content.getvalue()
                log.info(f"文件下载完成: {len(file_bytes)} 字节")

                return file_bytes

        except requests.exceptions.RequestException as e:
            status_code = (
                getattr(e.response, "status_code", None)
                if hasattr(e, "response") and e.response
                else None
            )
            raise FileDownloadError(
                f"文件下载失败: {str(e)}", url=url, status_code=status_code
            )
        except Exception as e:
            raise FileDownloadError(f"文件下载异常: {str(e)}", url=url)

    @log_function_call
    def convert_with_markitdown(
        self, file_content: bytes, file_extension: str, request_id: str = ""
    ) -> str:
        """
        使用MarkItDown直接转换文件为Markdown

        Args:
            file_content: 文件二进制内容
            file_extension: 文件扩展名 (.docx, .pdf)
            request_id: 请求ID

        Returns:
            str: Markdown格式内容
        """
        if not MARKITDOWN_AVAILABLE:
            raise ImportError("MarkItDown库不可用")

        temp_path = None
        try:
            with TimingContext("MarkItDown转换", request_id):
                # 创建临时文件
                with tempfile.NamedTemporaryFile(
                    delete=False, suffix=file_extension
                ) as temp_file:
                    temp_file.write(file_content)
                    temp_path = temp_file.name

                # 使用MarkItDown转换
                markitdown = MarkItDown()
                result = markitdown.convert(temp_path)
                markdown_content = result.text_content

                log.info(f"MarkItDown转换成功: {len(markdown_content)} 字符")
                return markdown_content

        except Exception as e:
            log.error(f"MarkItDown转换失败: {str(e)}")
            raise FileProcessingError(
                f"MarkItDown转换失败: {str(e)}",
                file_type=file_extension,
                processing_stage="MarkItDown转换",
                original_error=e,
            )
        finally:
            # 清理临时文件
            if temp_path and os.path.exists(temp_path):
                try:
                    os.unlink(temp_path)
                    log.debug(f"清理临时文件: {temp_path}")
                except Exception as e:
                    log.warning(f"清理临时文件失败: {str(e)}")

    @log_function_call
    def extract_docx_content_fallback(
        self, file_content: bytes, request_id: str = ""
    ) -> str:
        """
        备用DOCX内容提取方案
        """
        try:
            with TimingContext("DOCX备用提取", request_id):
                file_obj = BytesIO(file_content)
                doc = docx.Document(file_obj)

                content_parts = []

                # 提取段落
                for paragraph in doc.paragraphs:
                    text = paragraph.text.strip()
                    if text:
                        content_parts.append(text)

                # 提取表格
                for table in doc.tables:
                    table_rows = []
                    for row in table.rows:
                        row_cells = []
                        for cell in row.cells:
                            cell_text = cell.text.strip()
                            row_cells.append(cell_text)
                        if any(row_cells):
                            table_rows.append(" | ".join(row_cells))

                    if table_rows:
                        # 添加表格标题行分隔符
                        if len(table_rows) > 0:
                            header = table_rows[0]
                            separator = " | ".join(["---"] * len(header.split(" | ")))
                            content_parts.append(f"| {header} |")
                            content_parts.append(f"| {separator} |")

                            for row in table_rows[1:]:
                                content_parts.append(f"| {row} |")

                content = "\n\n".join(content_parts)
                log.info(f"DOCX备用提取完成: {len(content)} 字符")
                return content

        except Exception as e:
            raise FileProcessingError(
                f"DOCX备用处理失败: {str(e)}",
                file_type=".docx",
                processing_stage="DOCX内容提取",
                original_error=e,
            )

    @log_function_call
    def extract_pdf_content_fallback(
        self, file_content: bytes, request_id: str = ""
    ) -> str:
        """
        备用PDF内容提取方案
        """
        try:
            with TimingContext("PDF备用提取", request_id):
                file_obj = BytesIO(file_content)

                with pdfplumber.open(file_obj) as pdf:
                    content_parts = []

                    for page_num, page in enumerate(pdf.pages, 1):
                        try:
                            # 添加页面标记
                            content_parts.append(f"<!-- page:{page_num} -->")

                            # 提取文本
                            text = page.extract_text()
                            if text:
                                content_parts.append(text.strip())

                            # 提取表格
                            tables = page.extract_tables()
                            for table in tables:
                                if table:
                                    table_rows = []
                                    for row in table:
                                        if row and any(cell for cell in row if cell):
                                            row_text = " | ".join(
                                                str(cell or "") for cell in row
                                            )
                                            table_rows.append(row_text)

                                    if table_rows:
                                        # 添加表格格式
                                        header = table_rows[0]
                                        separator = " | ".join(
                                            ["---"] * len(header.split(" | "))
                                        )
                                        content_parts.append(f"| {header} |")
                                        content_parts.append(f"| {separator} |")

                                        for row in table_rows[1:]:
                                            content_parts.append(f"| {row} |")

                        except Exception as page_error:
                            log.warning(f"PDF第{page_num}页处理失败: {str(page_error)}")
                            continue

                content = "\n\n".join(content_parts)
                log.info(
                    f"PDF备用提取完成: {len(content)} 字符，共 {len(pdf.pages)} 页"
                )
                return content

        except Exception as e:
            raise FileProcessingError(
                f"PDF备用处理失败: {str(e)}",
                file_type=".pdf",
                processing_stage="PDF内容提取",
                original_error=e,
            )

    @log_function_call
    def process_file(self, file_info: FileInfo, request_id: str = "") -> str:
        """
        优化后的文件处理流程

        Args:
            file_info: 文件信息
            request_id: 请求ID

        Returns:
            str: Markdown格式的文件内容
        """
        try:
            with TimingContext("优化文件处理流程", request_id):
                # 1. 验证文件格式
                self.validate_file_format(file_info)

                # 2. 下载文件
                file_content = self.download_file(str(file_info.url), request_id)

                # 3. 优先使用MarkItDown直接转换
                try:
                    if MARKITDOWN_AVAILABLE:
                        markdown_content = self.convert_with_markitdown(
                            file_content, file_info.extension.value, request_id
                        )

                        # 记录处理结果
                        performance_logger.log_file_processing(
                            request_id, file_info.filename, file_info.size, 0
                        )

                        log.info(
                            f"文件处理完成(MarkItDown): {file_info.filename}, 输出长度: {len(markdown_content)} 字符"
                        )
                        return markdown_content
                    else:
                        log.warning("MarkItDown不可用，使用备用方案")
                        raise ImportError("MarkItDown不可用")

                except Exception as e:
                    log.warning(f"MarkItDown处理失败: {str(e)}，使用备用方案")

                # 4. 备用方案：传统提取 + 简单格式化
                if file_info.extension == FileExtension.DOCX:
                    raw_content = self.extract_docx_content_fallback(
                        file_content, request_id
                    )
                elif file_info.extension == FileExtension.PDF:
                    raw_content = self.extract_pdf_content_fallback(
                        file_content, request_id
                    )
                else:
                    raise FileProcessingError(
                        f"不支持的文件类型: {file_info.extension}",
                        filename=file_info.filename,
                        file_type=file_info.extension.value,
                        processing_stage="文件类型识别",
                    )

                # 5. 简单的Markdown格式化
                markdown_content = self._add_markdown_header(
                    raw_content, file_info.filename, file_info.extension.value
                )

                # 6. 记录处理结果
                performance_logger.log_file_processing(
                    request_id, file_info.filename, file_info.size, 0
                )

                log.info(
                    f"文件处理完成(备用方案): {file_info.filename}, 输出长度: {len(markdown_content)} 字符"
                )
                return markdown_content

        except (FileDownloadError, FileProcessingError, FileFormatError):
            raise
        except Exception as e:
            raise FileProcessingError(
                f"文件处理失败: {str(e)}",
                filename=file_info.filename,
                file_type=file_info.extension.value,
                processing_stage="整体处理流程",
                original_error=e,
            )

    def _add_markdown_header(self, content: str, filename: str, file_type: str) -> str:
        """
        为内容添加Markdown标题

        Args:
            content: 原始内容
            filename: 文件名
            file_type: 文件类型

        Returns:
            str: 带标题的Markdown内容
        """
        header = f"# {filename}\n\n**文件类型**: {file_type.upper()}\n\n---\n\n"
        return header + content

    def get_processing_stats(self) -> dict:
        """
        获取处理统计信息

        Returns:
            dict: 处理统计信息
        """
        return {
            "markitdown_available": MARKITDOWN_AVAILABLE,
            "supported_formats": [".docx", ".pdf"],
            "max_file_size_mb": settings.max_file_size / 1024 / 1024,
            "request_timeout": settings.request_timeout,
            "max_retries": settings.max_retries,
        }

    def validate_processing_capability(self, file_info: FileInfo) -> dict:
        """
        验证处理能力

        Args:
            file_info: 文件信息

        Returns:
            dict: 处理能力评估
        """
        capability = {
            "can_process": False,
            "preferred_method": None,
            "estimated_time": 0,
            "warnings": [],
        }

        try:
            # 验证格式
            self.validate_file_format(file_info)
            capability["can_process"] = True

            # 确定处理方法
            if MARKITDOWN_AVAILABLE:
                capability["preferred_method"] = "MarkItDown"
            else:
                capability["preferred_method"] = "传统提取"
                capability["warnings"].append("MarkItDown不可用，将使用备用方案")

            # 估算处理时间（使用内联逻辑避免导入问题）
            try:
                size_mb = file_info.size / (1024 * 1024)
                base_time = 1.0

                if file_info.extension.value == ".pdf":
                    capability["estimated_time"] = base_time + size_mb * 2.0
                elif file_info.extension.value == ".docx":
                    capability["estimated_time"] = base_time + size_mb * 1.0
                else:
                    capability["estimated_time"] = base_time + size_mb * 1.5

                log.debug(f"处理时间估算: {capability['estimated_time']:.2f}秒")

            except Exception as e:
                log.warning(f"处理时间估算失败: {str(e)}")
                capability["estimated_time"] = 1.0

            # 大文件警告
            if file_info.size > 10 * 1024 * 1024:  # 10MB
                capability["warnings"].append("大文件可能需要较长处理时间")

        except (FileFormatError, FileProcessingError) as e:
            capability["can_process"] = False
            capability["warnings"].append(str(e))

        return capability


# 创建全局优化文件处理器实例
optimized_file_processor = OptimizedFileProcessor()
