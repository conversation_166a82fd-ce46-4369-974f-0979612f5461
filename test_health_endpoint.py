#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
健康检查端点测试
"""

import requests
import time


def test_health_endpoint(base_url="http://localhost:8088"):
    """测试健康检查端点"""
    print(f"测试健康检查端点: {base_url}/health")

    try:
        response = requests.get(f"{base_url}/health", timeout=5)

        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")

        if response.status_code == 200:
            data = response.json()
            print(f"响应内容: {data}")
            print("✅ 健康检查成功")
            return True
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            print(f"错误内容: {response.text}")
            return False

    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
        return False


def test_root_endpoint(base_url="http://localhost:8088"):
    """测试根端点"""
    print(f"\n测试根端点: {base_url}/")

    try:
        response = requests.get(f"{base_url}/", timeout=5)

        print(f"状态码: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            print(f"响应内容: {data}")
            print("✅ 根端点访问成功")
            return True
        else:
            print(f"❌ 根端点访问失败: {response.status_code}")
            print(f"错误内容: {response.text}")
            return False

    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
        return False


def main():
    """主函数"""
    print("健康检查端点测试")
    print("=" * 50)

    # 等待服务启动
    print("等待服务启动...")
    time.sleep(2)

    # 测试健康检查端点
    health_ok = test_health_endpoint()

    # 测试根端点
    root_ok = test_root_endpoint()

    print("\n" + "=" * 50)
    print("测试总结")
    print("=" * 50)

    if health_ok and root_ok:
        print("🎉 所有端点测试通过！")
        return 0
    else:
        print("❌ 部分端点测试失败")
        return 1


if __name__ == "__main__":
    exit(main())
