#!/usr/bin/env python3
"""
测试改进的AI提示词
"""

import sys
import os
import json
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_improved_prompt():
    """测试改进的AI提示词"""
    print("开始测试改进的AI提示词...")
    
    try:
        from app.services.ai_model_service import AIModelService
        from app.models.schemas import ProjectInfo
        from app.models.enums import (
            ProcurementProjectType,
            ProjectCategory,
            BiddingProcurementMethod,
            QuestionType,
        )
        
        ai_service = AIModelService()
        
        # 创建测试项目信息
        project_info = ProjectInfo(
            procurement_project_type=ProcurementProjectType.GOODS,
            project_category=ProjectCategory.GOVERNMENT_PROCUREMENT,
            bidding_procurement_method=BiddingProcurementMethod.PUBLIC_BIDDING,
        )
        
        # 创建包含明显问题的测试内容
        test_content = """
        招标公告
        
        一、项目基本情况
        项目编号：TEST-2025-001
        项目名称：某品牌电脑采购项目
        采购预算：未明确
        
        二、投标人资格要求
        1. 必须是某某品牌的授权代理商
        2. 注册资金不少于1000万元
        3. 近三年年均营业额不少于5000万元
        
        三、技术要求
        1. 必须提供某某品牌产品
        2. 配置要求：最好的处理器，最优秀的显卡
        3. 质量要求：达到世界一流水平
        
        四、商务条款
        1. 投标保证金：未明确
        2. 履约保证金：未明确
        3. 付款方式：验收后付款（未明确具体时间）
        
        五、评标办法
        价格分占100%
        
        六、其他要求
        1. 投标文件必须使用指定格式
        2. 开标时间：待定
        3. 联系方式：详见附件（但未提供附件）
        """
        
        print(f"测试内容长度: {len(test_content)} 字符")
        print("\n测试内容包含的明显问题:")
        print("- 指定品牌（违反公平竞争原则）")
        print("- 预算未明确")
        print("- 主观评价词汇（最好的、最优秀的、一流）")
        print("- 投标保证金未明确")
        print("- 评标办法不合理（价格分100%）")
        print("- 开标时间待定")
        
        print("\n开始AI合规性检查...")
        start_time = time.time()
        
        try:
            result = ai_service.check_compliance(test_content, project_info, "improved-prompt-test")
            end_time = time.time()
            
            duration = end_time - start_time
            print(f"\n✅ AI模型调用成功!")
            print(f"调用时间: {duration:.2f}秒")
            print(f"发现问题数量: {len(result.checkResultArr)}")
            
            if len(result.checkResultArr) > 0:
                print("\n发现的问题:")
                for i, item in enumerate(result.checkResultArr):
                    print(f"  {i+1}. 类型: {item.quesType}")
                    print(f"     描述: {item.quesDesc[:150]}...")
                    print(f"     建议: {item.advice[:100]}...")
                    print()
                
                # 统计问题类型
                type_counts = {}
                for item in result.checkResultArr:
                    type_counts[item.quesType] = type_counts.get(item.quesType, 0) + 1
                
                print("问题类型统计:")
                for qtype, count in type_counts.items():
                    print(f"  {qtype}: {count}个")
                
                if len(result.checkResultArr) >= 5:
                    print("\n✅ AI模型能够识别多种类型的合规性问题")
                else:
                    print("\n⚠️  AI模型识别的问题较少，可能需要进一步优化提示词")
            else:
                print("\n❌ 未发现任何问题，这明显不正常！")
                print("可能的原因:")
                print("1. AI模型提示词需要进一步优化")
                print("2. AI模型响应格式仍有问题")
                print("3. JSON解析逻辑存在bug")
            
            return len(result.checkResultArr) > 0
            
        except Exception as e:
            print(f"\n❌ AI模型调用失败: {e}")
            import traceback
            traceback.print_exc()
            return False
            
    except Exception as e:
        print(f"❌ 测试初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_improved_prompt()
    if success:
        print("\n🎉 测试成功！AI模型能够正确识别合规性问题。")
    else:
        print("\n❌ 测试失败，需要进一步调试。")
