#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
逐步测试端点中的每个操作
"""

import requests
import json
import time


def test_step_1_fileinfo_creation():
    """测试步骤1：FileInfo创建"""
    print("步骤1：测试FileInfo创建端点")
    print("-" * 40)

    base_url = "http://localhost:8088"

    # 创建一个只测试FileInfo创建的端点
    endpoint_code = '''
@router.post("/debug/test-fileinfo")
async def test_fileinfo_creation(file_info_data: dict):
    """只测试FileInfo创建"""
    try:
        from app.models.schemas import FileInfo
        file_info = FileInfo(**file_info_data)
        return {
            "status": "success",
            "message": "FileInfo创建成功",
            "filename": file_info.filename
        }
    except Exception as e:
        return {"status": "error", "error": str(e)}
'''

    print("需要添加以下端点到routes.py:")
    print(endpoint_code)

    # 测试这个端点
    file_info_data = {
        "filename": "test.docx",
        "extension": ".docx",
        "mime_type": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "size": 1024,
        "url": "http://example.com/test.docx",
    }

    try:
        start_time = time.time()
        response = requests.post(
            f"{base_url}/api/v1/debug/test-fileinfo", json=file_info_data, timeout=10
        )
        elapsed = time.time() - start_time

        print(f"响应时间: {elapsed:.2f}秒")
        print(f"状态码: {response.status_code}")

        if response.status_code == 200:
            print("✅ FileInfo创建端点正常")
            return True
        else:
            print(f"❌ FileInfo创建端点失败: {response.text}")
            return False

    except requests.exceptions.Timeout:
        print("❌ FileInfo创建端点超时")
        return False
    except Exception as e:
        print(f"❌ FileInfo创建端点异常: {str(e)}")
        return False


def test_step_2_processor_call():
    """测试步骤2：处理器调用"""
    print("\n步骤2：测试处理器调用端点")
    print("-" * 40)

    base_url = "http://localhost:8088"

    endpoint_code = '''
@router.post("/debug/test-processor")
async def test_processor_call(file_info_data: dict):
    """测试处理器调用"""
    try:
        from app.models.schemas import FileInfo
        file_info = FileInfo(**file_info_data)
        
        # 调用处理器
        capability = file_processor.validate_processing_capability(file_info)
        
        return {
            "status": "success",
            "message": "处理器调用成功",
            "can_process": capability.get("can_process", False)
        }
    except Exception as e:
        return {"status": "error", "error": str(e)}
'''

    print("需要添加以下端点到routes.py:")
    print(endpoint_code)

    # 测试这个端点
    file_info_data = {
        "filename": "test.docx",
        "extension": ".docx",
        "mime_type": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "size": 1024,
        "url": "http://example.com/test.docx",
    }

    try:
        start_time = time.time()
        response = requests.post(
            f"{base_url}/api/v1/debug/test-processor", json=file_info_data, timeout=10
        )
        elapsed = time.time() - start_time

        print(f"响应时间: {elapsed:.2f}秒")
        print(f"状态码: {response.status_code}")

        if response.status_code == 200:
            result = response.json()
            print("✅ 处理器调用端点正常")
            print(f"处理结果: {result}")
            return True
        else:
            print(f"❌ 处理器调用端点失败: {response.text}")
            return False

    except requests.exceptions.Timeout:
        print("❌ 处理器调用端点超时")
        return False
    except Exception as e:
        print(f"❌ 处理器调用端点异常: {str(e)}")
        return False


def test_current_endpoint_with_logging():
    """测试当前端点并添加详细日志"""
    print("\n当前端点测试（带详细日志）")
    print("-" * 40)

    # 建议在当前端点中添加日志
    logging_suggestion = """
# 在validate_file_info函数中添加详细日志：

async def validate_file_info(request: Request, file_info_data: Dict[str, Any]) -> Dict[str, Any]:
    request_id = getattr(request.state, "request_id", "")
    
    print(f"[DEBUG] 开始处理文件验证请求 | ID: {request_id}")
    
    try:
        print(f"[DEBUG] 导入FileInfo模块...")
        from app.models.schemas import FileInfo
        
        print(f"[DEBUG] 创建FileInfo对象...")
        file_info = FileInfo(**file_info_data)
        print(f"[DEBUG] FileInfo创建成功: {file_info.filename}")
        
        print(f"[DEBUG] 调用处理器...")
        capability = file_processor.validate_processing_capability(file_info)
        print(f"[DEBUG] 处理器调用成功")
        
        print(f"[DEBUG] 准备返回结果...")
        file_info_dict = {
            "filename": file_info.filename,
            "extension": file_info.extension.value,
            "mime_type": file_info.mime_type.value,
            "size": file_info.size,
            "url": str(file_info.url)
        }
        
        print(f"[DEBUG] 返回结果")
        return {"file_info": file_info_dict, "processing_capability": capability}
        
    except Exception as e:
        print(f"[DEBUG] 异常发生: {str(e)}")
        return {"error": str(e), "status": "failed"}
"""

    print("建议在validate_file_info函数中添加以下调试日志:")
    print(logging_suggestion)

    return True


def main():
    """主函数"""
    print("逐步调试端点操作")
    print("=" * 60)

    print("我们需要创建更细粒度的测试端点来定位问题")
    print("=" * 60)

    # 步骤1：测试FileInfo创建
    step1_success = test_step_1_fileinfo_creation()

    # 步骤2：测试处理器调用
    step2_success = test_step_2_processor_call()

    # 当前端点日志建议
    test_current_endpoint_with_logging()

    print(f"\n{'='*60}")
    print("测试结果总结")
    print("=" * 60)
    print(f"步骤1 (FileInfo创建): {'✅ 通过' if step1_success else '❌ 失败'}")
    print(f"步骤2 (处理器调用): {'✅ 通过' if step2_success else '❌ 失败'}")

    print(f"\n{'='*60}")
    print("下一步行动计划")
    print("=" * 60)
    if step1_success and step2_success:
        print("✅ 所有调试端点都正常工作")
        print("现在可以测试完整的 /validate-file 端点并观察详细日志")
    elif step1_success and not step2_success:
        print("❌ 处理器调用有问题，需要检查 file_processor 模块")
        print("建议检查 file_processor.validate_processing_capability 方法")
    elif not step1_success:
        print("❌ FileInfo创建有问题，需要检查 schemas 模块")
        print("建议检查 FileInfo 模型定义")
    else:
        print("❌ 多个组件有问题，需要逐一排查")


if __name__ == "__main__":
    main()
