#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试最简单的端点
"""

import requests
import json
import time


def test_simple_endpoint():
    """测试最简单的端点"""
    print("测试最简单的端点")
    print("=" * 50)

    base_url = "http://localhost:8088"

    test_data = {"test": "data"}

    try:
        start_time = time.time()

        response = requests.post(
            f"{base_url}/api/v1/debug/simple-test", json=test_data, timeout=10
        )

        elapsed = time.time() - start_time
        print(f"响应时间: {elapsed:.2f}秒")
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.text}")

        if response.status_code == 200:
            print("✅ 简单端点正常")
            return True
        else:
            print("❌ 简单端点失败")
            return False

    except Exception as e:
        elapsed = time.time() - start_time
        print(f"❌ 简单端点异常，耗时: {elapsed:.2f}秒，错误: {str(e)}")
        return False


def test_file_info_creation():
    """测试FileInfo对象创建"""
    print("\n测试FileInfo对象创建")
    print("=" * 50)

    base_url = "http://localhost:8088"

    # 模拟FileInfo数据
    file_info_data = {
        "filename": "test.docx",
        "extension": ".docx",
        "mime_type": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "size": 1024,
        "url": "http://example.com/test.docx",
    }

    try:
        start_time = time.time()

        response = requests.post(
            f"{base_url}/api/v1/debug/simple-test", json=file_info_data, timeout=10
        )

        elapsed = time.time() - start_time
        print(f"响应时间: {elapsed:.2f}秒")
        print(f"状态码: {response.status_code}")

        if response.status_code == 200:
            print("✅ FileInfo数据传输正常")
            return True
        else:
            print("❌ FileInfo数据传输失败")
            print(f"响应: {response.text}")
            return False

    except Exception as e:
        elapsed = time.time() - start_time
        print(f"❌ FileInfo数据传输异常，耗时: {elapsed:.2f}秒，错误: {str(e)}")
        return False


def main():
    """主函数"""
    print("最简单端点测试")
    print("=" * 60)

    # 测试最简单的端点
    simple_ok = test_simple_endpoint()

    # 测试FileInfo数据传输
    fileinfo_ok = test_file_info_creation()

    print(f"\n{'='*60}")
    print("结果分析")
    print("=" * 60)

    if simple_ok and fileinfo_ok:
        print("✅ 基础POST请求正常，问题在于文件验证逻辑")
    elif simple_ok and not fileinfo_ok:
        print("🔍 问题可能在于数据量或数据格式")
    elif not simple_ok:
        print("❌ 基础POST请求都有问题，可能是中间件或服务器配置问题")


if __name__ == "__main__":
    main()
