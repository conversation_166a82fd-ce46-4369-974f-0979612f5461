2025-08-12 12:46:53 | INFO     | app.middleware.logging:dispatch:48 | 请求完成 | ID: a6ac4e1d-c589-427d-a2bb-9fc32fe328bb | 状态码: 405 | 耗时: 0.005秒
2025-08-12 12:46:54 | INFO     | app.middleware.logging:dispatch:35 | 请求开始 | ID: 96056d98-de6d-4ce9-b66e-18d9346b4f3c | 方法: GET | URL: http://*************:8088/favicon.ico | 客户端IP: ************* | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
2025-08-12 12:46:54 | INFO     | app.middleware.logging:dispatch:48 | 请求完成 | ID: 96056d98-de6d-4ce9-b66e-18d9346b4f3c | 状态码: 404 | 耗时: 0.002秒
2025-08-12 14:58:32 | INFO     | app.middleware.logging:dispatch:35 | 请求开始 | ID: 63940a12-bdde-4769-9439-ab3089f835b2 | 方法: POST | URL: http://*************:8088/api/v1/check-compliance-simple | 客户端IP: ************* | User-Agent: Apifox/1.0.0 (https://apifox.com)
2025-08-12 14:58:32 | INFO     | app.middleware.validation:_validate_request:75 | 跳过中间件验证 | ID: 63940a12-bdde-4769-9439-ab3089f835b2 | 路径: /api/v1/check-compliance-simple
2025-08-12 14:58:32 | INFO     | app.api.routes:check_compliance_simple:125 | 开始简化合规性检查 | ID: 63940a12-bdde-4769-9439-ab3089f835b2 | URL: https://test-fds.xinecai.com/group1/M00/02/93/rBIKBmiatqqAXC4lABaSuc9ZQzU547.doc?token=894b64823d6e7b39f52d03ee0cffff92&ts=1754969781
2025-08-12 14:58:32 | INFO     | app.utils.file_info_utils:infer_file_info_from_url:154 | 开始从URL推断文件信息: https://test-fds.xinecai.com/group1/M00/02/93/rBIKBmiatqqAXC4lABaSuc9ZQzU547.doc?token=894b64823d6e7b39f52d03ee0cffff92&ts=1754969781
2025-08-12 14:58:32 | INFO     | app.utils.file_info_utils:infer_file_info_from_url:158 | 推断的文件名: rBIKBmiatqqAXC4lABaSuc9ZQzU547.doc
2025-08-12 14:58:32 | WARNING  | app.utils.file_info_utils:infer_file_extension_and_mime_type:107 | 未识别的文件扩展名: .doc，默认使用docx格式
2025-08-12 14:58:32 | INFO     | app.utils.file_info_utils:infer_file_info_from_url:162 | 推断的扩展名: .docx, MIME类型: application/vnd.openxmlformats-officedocument.wordprocessingml.document
2025-08-12 14:58:33 | INFO     | app.utils.file_info_utils:infer_file_info_from_url:171 | 获取的文件大小: 1479353 字节
2025-08-12 14:58:33 | INFO     | app.utils.file_info_utils:infer_file_info_from_url:182 | 文件信息推断完成: rBIKBmiatqqAXC4lABaSuc9ZQzU547.doc
2025-08-12 14:58:33 | INFO     | app.api.routes:check_compliance_simple:131 | 文件信息推断成功 | ID: 63940a12-bdde-4769-9439-ab3089f835b2 | 文件: rBIKBmiatqqAXC4lABaSuc9ZQzU547.doc
2025-08-12 14:58:33 | INFO     | app.services.compliance_service:check_compliance:636 | 合规性检查服务开始 | ID: 63940a12-bdde-4769-9439-ab3089f835b2
2025-08-12 14:58:33 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 完整合规性检查流水线 | ID: 63940a12-bdde-4769-9439-ab3089f835b2 | 详情: {}
2025-08-12 14:58:33 | INFO     | app.services.compliance_service:execute_pipeline:376 | 开始执行合规性检查流水线 | ID: 63940a12-bdde-4769-9439-ab3089f835b2 | 文件: rBIKBmiatqqAXC4lABaSuc9ZQzU547.doc
2025-08-12 14:58:33 | INFO     | app.services.compliance_service:execute_pipeline:386 | 步骤1: 开始验证服务前置条件 | ID: 63940a12-bdde-4769-9439-ab3089f835b2
2025-08-12 14:58:33 | INFO     | app.services.compliance_service:validate_prerequisites:73 | 开始验证服务前置条件 | ID: 63940a12-bdde-4769-9439-ab3089f835b2
2025-08-12 14:58:33 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 敏感词服务健康检查 | ID: 63940a12-bdde-4769-9439-ab3089f835b2 | 详情: {}
2025-08-12 14:58:33 | INFO     | app.services.sensitive_word_service:check_health:122 | 检查敏感词服务健康状态: http://************:8087/health
2025-08-12 14:58:33 | INFO     | app.services.sensitive_word_service:check_health:132 | 敏感词服务健康状态: 健康
2025-08-12 14:58:33 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 敏感词服务健康检查 | ID: 63940a12-bdde-4769-9439-ab3089f835b2 | 状态: 成功 | 耗时: 0.050秒
2025-08-12 14:58:33 | INFO     | app.services.compliance_service:validate_prerequisites:92 | 服务健康检查完成 | ID: 63940a12-bdde-4769-9439-ab3089f835b2 | 状态: {'file_processor': True, 'ai_model': True, 'sensitive_word': True, 'result_processor': True}
2025-08-12 14:58:33 | INFO     | app.services.compliance_service:execute_pipeline:388 | 步骤1: 服务前置条件验证完成 | ID: 63940a12-bdde-4769-9439-ab3089f835b2 | 状态: {'file_processor': True, 'ai_model': True, 'sensitive_word': True, 'result_processor': True}
2025-08-12 14:58:33 | INFO     | app.services.compliance_service:execute_pipeline:394 | 步骤2: 开始获取项目信息 | ID: 63940a12-bdde-4769-9439-ab3089f835b2
2025-08-12 14:58:33 | INFO     | app.services.compliance_service:execute_pipeline:396 | 步骤2: 项目信息获取完成 | ID: 63940a12-bdde-4769-9439-ab3089f835b2
2025-08-12 14:58:33 | INFO     | app.services.compliance_service:execute_pipeline:400 | 步骤3: 开始文件处理阶段 | ID: 63940a12-bdde-4769-9439-ab3089f835b2
2025-08-12 14:58:33 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 文件处理阶段 | ID: 63940a12-bdde-4769-9439-ab3089f835b2 | 详情: {}
2025-08-12 14:58:33 | INFO     | app.services.compliance_service:process_file_stage:114 | 开始文件处理阶段 | ID: 63940a12-bdde-4769-9439-ab3089f835b2 | 文件: rBIKBmiatqqAXC4lABaSuc9ZQzU547.doc
2025-08-12 14:58:33 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 优化文件处理流程 | ID: 63940a12-bdde-4769-9439-ab3089f835b2 | 详情: {}
2025-08-12 14:58:33 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 文件下载 | ID: 63940a12-bdde-4769-9439-ab3089f835b2 | 详情: {}
2025-08-12 14:58:33 | INFO     | app.services.file_processor_v2:download_file:255 | 开始下载文件: https://test-fds.xinecai.com/group1/M00/02/93/rBIKBmiatqqAXC4lABaSuc9ZQzU547.doc?token=894b64823d6e7b39f52d03ee0cffff92&ts=1754969781
2025-08-12 14:58:34 | INFO     | app.services.file_processor_v2:download_file:289 | 文件下载完成: 1479353 字节
2025-08-12 14:58:34 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 文件下载 | ID: 63940a12-bdde-4769-9439-ab3089f835b2 | 状态: 成功 | 耗时: 1.311秒
2025-08-12 14:58:34 | WARNING  | app.services.file_processor_v2:process_file:515 | MarkItDown不可用，使用备用方案
2025-08-12 14:58:34 | WARNING  | app.services.file_processor_v2:process_file:519 | MarkItDown处理失败: MarkItDown不可用，使用备用方案
2025-08-12 14:58:34 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | DOCX备用提取 | ID: 63940a12-bdde-4769-9439-ab3089f835b2 | 详情: {}
2025-08-12 14:58:35 | INFO     | app.services.file_processor_v2:extract_docx_content_fallback:401 | DOCX备用提取完成: 36623 字符
2025-08-12 14:58:35 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | DOCX备用提取 | ID: 63940a12-bdde-4769-9439-ab3089f835b2 | 状态: 成功 | 耗时: 0.194秒
2025-08-12 14:58:35 | INFO     | app.core.logger:log_file_processing:159 | 文件处理 | ID: 63940a12-bdde-4769-9439-ab3089f835b2 | 文件: rBIKBmiatqqAXC4lABaSuc9ZQzU547.doc | 大小: 1479353 字节 | 处理时间: 0.000秒
2025-08-12 14:58:35 | INFO     | app.services.file_processor_v2:process_file:548 | 文件处理完成(备用方案): rBIKBmiatqqAXC4lABaSuc9ZQzU547.doc, 输出长度: 36683 字符
2025-08-12 14:58:35 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 优化文件处理流程 | ID: 63940a12-bdde-4769-9439-ab3089f835b2 | 状态: 成功 | 耗时: 1.507秒
2025-08-12 14:58:35 | INFO     | app.services.compliance_service:process_file_stage:131 | 文件处理完成 | ID: 63940a12-bdde-4769-9439-ab3089f835b2 | 内容长度: 36683 字符
2025-08-12 14:58:35 | INFO     | app.core.logger:log_file_processing:159 | 文件处理 | ID: 63940a12-bdde-4769-9439-ab3089f835b2 | 文件: rBIKBmiatqqAXC4lABaSuc9ZQzU547.doc | 大小: 1479353 字节 | 处理时间: 0.000秒
2025-08-12 14:58:35 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | file_processing | ID: 63940a12-bdde-4769-9439-ab3089f835b2 | 状态: 成功 | 耗时: 1.511秒
2025-08-12 14:58:35 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 文件处理阶段 | ID: 63940a12-bdde-4769-9439-ab3089f835b2 | 状态: 成功 | 耗时: 1.511秒
2025-08-12 14:58:35 | INFO     | app.services.compliance_service:execute_pipeline:405 | 步骤3: 文件处理完成 | ID: 63940a12-bdde-4769-9439-ab3089f835b2 | 内容长度: 36683
2025-08-12 14:58:35 | INFO     | app.services.compliance_service:execute_pipeline:416 | 步骤4: 开始AI合规性检查阶段 | ID: 63940a12-bdde-4769-9439-ab3089f835b2
2025-08-12 14:58:35 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | AI合规性检查阶段 | ID: 63940a12-bdde-4769-9439-ab3089f835b2 | 详情: {}
2025-08-12 14:58:35 | INFO     | app.services.compliance_service:ai_compliance_check_stage:188 | 开始AI合规性检查 | ID: 63940a12-bdde-4769-9439-ab3089f835b2
2025-08-12 14:58:35 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | AI模型调用(尝试1) | ID: 63940a12-bdde-4769-9439-ab3089f835b2 | 详情: {}
2025-08-12 14:58:35 | INFO     | app.services.ai_model_service:_call_model_with_retry:770 | 调用AI模型: gemini-2.5-flash, 消息数: 2, 尝试: 1/4 | 请求ID: 63940a12-bdde-4769-9439-ab3089f835b2
2025-08-12 15:02:08 | INFO     | app.core.logger:log_api_call:170 | API调用 | OpenAI-gemini-2.5-flash | ID: 63940a12-bdde-4769-9439-ab3089f835b2 | 状态: 成功 | 耗时: 0.000秒
2025-08-12 15:02:08 | INFO     | app.services.ai_model_service:_call_model_with_retry:809 | AI模型调用成功: 响应长度 4229 字符, 尝试次数: 1 | 请求ID: 63940a12-bdde-4769-9439-ab3089f835b2
2025-08-12 15:02:08 | WARNING  | app.services.ai_model_service:_call_model_with_retry:813 | AI模型原始响应内容: '```json
{
  "checkResultArr": [
    {
      "quesType": "规范性",
      "quesDesc": "招标公告中项目名称、项目编号、招标人信息为占位符，未填写具体内容。原文位置：第一章 招标公告 一、招标条件 1、项目名称、2、项目编号、3、招标人",
      "originalArr": [
        "1、项目名称：项目名称",
        "2、项目编号：项目编号",
        "3、招标人：\n☐ 是  ☑ 否"
      ],
      "point": "项目信息完整性与准确性",
      "advice": "请在招标公告中补充完整的项目名称、项目编号和招标人名称，确保信息明确无误。"
    },
    {
      "quesType": "逻辑性",
      "quesDesc": "招标文件在“投标人须知前附表”中明确“不允许分包”，但在“第四章 投标文件格式”的“投标承诺函”中又提及“对招标方允许分包的部分内容”，存在矛盾。原文位置：投标人须知前附表 是否允许分包；第四章 投标文件格式 投标承诺函 第7条",
      "originalArr": [
        "是否允许分包\n不允许分包",
        "我司承诺不将设备整体转包给第三方。对招标方允许分包的部分内容，我司需提供备选分包商资质、能力、业绩等举证材料，供招标方审核，经招标方确认同意后才允许选用。且在项目实施过程中需提供与分包商签署的技术文件供招标方确认。倘若在项目安装过程中，我司选择的分包商不满足招标方对进度、质量的要求，招标方有权要求投标方立即更换分包商。"
      ],
      "point": "条款一致性与可操作性",
      "advice": "请明确本项目是否允许分包。若允许分包，需在投标人须知前附表中明确分包的条件和范围；若不允许分包，则删除投标承诺函中关于分包的描述，以避免歧义。"
    },
    {
      "quesType": "合规性",
      "quesDesc": "招标文件中的投标保证金金额和最高投标限价均为占位符，无法核实其是否符合相关规定（如投标保证金不超过项目预算的2%）。原文位置：投标人须知前附表 投标保证金；投标人须知前附表 最高投标限价",
      "originalArr": [
        "投标保证金\n人民币X万元整（¥X0,000.00）",
        "最高投标限价\n人民币X万元（含13%增值税）"
      ],
      "point": "投标保证金与最高投标限价的合规性",
      "advice": "请填写具体的投标保证金金额和最高投标限价，并确保投标保证金金额不超过项目预算的2%。"
    },
    {
      "quesType": "风险管理",
      "quesDesc": "评标办法中规定，评标委员会将对投标文件进行雷同性检查，并以MAC地址信息相同作为判定无效标的依据。MAC地址信息可能因网络环境、VPN使用等原因导致误判，存在误伤无辜投标人的风险。原文位置：第二章 投标人须知 20、评标 20.7",
      "originalArr": [
        "评标委员会应对投标文件进行雷同性检查，评标系统显示两家及以上投标人的投标文件MAC地址信息相同的，由评标委员会判定均按无效标处理。"
      ],
      "point": "评标公平性与风险控制",
      "advice": "建议修改或删除以MAC地址信息作为无效标的唯一判定依据，可结合其他证据（如投标文件内容高度雷同、报价异常一致等）综合判断是否存在串通投标行为，以避免误判，确保评标的公平性。"
    },
    {
      "quesType": "公平性",
      "quesDesc": "评标办法中规定，评标期间要求投标人对业绩证明材料进行澄清时，须在半个小时内提供。此时间限制过于严苛，可能导致投标人因客观原因（如网络延迟、人员不在岗等）无法及时响应而被否决投标，影响公平竞争。原文位置：第二章 投标人须知 20、评标 20.8",
      "originalArr": [
        "在评标期间，评标委员会就业绩证明材料要求投标人进一步提供澄清时，投标人须在半个小时内提供，否则造成投标被否决等情形，责任由投标人自负。业绩有其他要求的除外。"
      ],
      "point": "澄清时限的合理性",
      "advice": "建议将澄清回复时间延长至至少2小时或更长时间（如半个工作日），以给予投标人合理的响应时间，确保评标的公平性。"
    },
    {
      "quesType": "规范性",
      "quesDesc": "本项目采购项目类型为“服务类”，但招标代理服务费的收取标准却按“货物类”标准执行，分类不一致。原文位置：第二章 投标人须知 25、中标服务费 25.1",
      "originalArr": [
        "本次招标代理服务费按(原计价格[2002]1980号)文货物类的标准收取。由中标人在领取中标通知书时一次性支付给代理机构，也可以从投标保证金中扣除。"
      ],
      "point": "服务费收取标准的规范性",
      "advice": "请根据项目实际类型（服务类）明确招标代理服务费的收取标准，确保分类一致性。"
    },
    {
      "quesType": "合规性",
      "quesDesc": "作为服务类项目，招标文件未明确服务期限。原文位置：第一章 招标公告 二、项目概况与规模 1、招标范围；第五章 招标项目要求及技术规格",
      "originalArr": [
        "本次项目实施内容为安徽江淮汽车集团股份有限公司XXXXX项目的设计、制造、包装运输、货物装卸、安装、调试、培训及售后服务等。该项目为交钥匙工程。",
        "第五章 招标项目要求及技术规格"
      ],
      "point": "服务期限的明确性",
      "advice": "请在招标文件第五章“招标项目要求及技术规格”或第一章“招标公告”中明确具体的服务期限，以符合服务类项目的要求。"
    },
    {
      "quesType": "合规性",
      "quesDesc": "作为服务类项目，招标文件未具体明确服务质量标准和考核指标，仅提及“技术标准按国家、行业的相关标准及双方签订的《技术协议》执行”。原文位置：第三章 合同格式 第七章 终验收及培训 7.1",
      "originalArr": [
        "技术标准按国家、行业的相关标准及双方签订的《技术协议》执行。特种设备首检合格、需要进行检定的计量器具，检定合格等是验收的前提条件。(设备稳定运行3个月以上；节拍达标（如有）、问题消项率达标、陪产服务提供完成、验收资料提交完成并合格等是终验收前提)。"
      ],
      "point": "服务质量标准和考核指标的明确性",
      "advice": "请在招标文件第五章“招标项目要求及技术规格”中详细列出服务质量标准和具体的考核指标，以便投标人明确服务要求和评标时进行量化评估。"
    },
    {
      "quesType": "合规性",
      "quesDesc": "作为服务类项目，招标文件未明确服务人员的资质要求，仅在合同格式中提及“乙方特种作业人员须持证上岗”。原文位置：第三章 合同格式 第六章 安装、调试 6.8",
      "originalArr": [
        "乙方特种作业人员须持证上岗。"
      ],
      "point": "服务人员资质要求的明确性",
      "advice": "请在招标文件第五章“招标项目要求及技术规格”中明确本项目所需服务人员的资质、经验、数量等具体要求，确保服务质量。"
    },
    {
      "quesType": "规范性",
      "quesDesc": "评标办法中多处评分项的优秀、良好、一般等级的得分区间定义不精确，例如“优秀的得（7,10]分”，下限为开区间，可能导致评分歧义。原文位置：第六章 评标办法 二、详细评审 2.1 商务部分 综合实力；2.2 技术部分 产品技术；2.2 技术部分 项目管理与实施；2.3 服务部分 售后服务、培训服务及其他服务承诺",
      "originalArr": [
        "评标委员会根据投标人提供的下述材料，对投标人实力进行综合评审，优秀的得（7,10]分，良好的得（3,7]分，一般的得[0,3]分。满分10分。",
        "评标委员会根据投标人提供的下述材料，对投标人所投产品技术进行综合评审，优秀的得（20,30]分，良好的得（10,20]分，一般的得[0,10]分。满分30分。",
        "评标委员会根据投标人提供的下述材料，对投标人项目管理及实施方案进行综合评审，优秀的得（7,10]分，良好的得（3,7]分，一般的得[0,3]分。满分10分。",
        "评标委员会根据投标人提供的下述材料，对投标人售后服务、培训服务及其他服务方案进行综合评审，优秀的得（7,10]分，良好的得（3,7]分，一般的得[0,3]分。满分10分。"
      ],
      "point": "评分区间定义的精确性",
      "advice": "建议将所有评分项的得分区间修改为闭区间，例如“优秀的得[7,10]分，良好的得[3,7)分，一般的得[0,3)分”，以消除歧义，确保评分的准确性。"
    }
  ]
}
```'
2025-08-12 15:02:08 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | AI模型调用(尝试1) | ID: 63940a12-bdde-4769-9439-ab3089f835b2 | 状态: 成功 | 耗时: 212.843秒
2025-08-12 15:02:08 | INFO     | app.services.ai_model_service:check_compliance:1090 | 开始清理AI响应JSON数据 | 请求ID: 63940a12-bdde-4769-9439-ab3089f835b2 | 原始长度: 4229 字符
2025-08-12 15:02:08 | INFO     | app.services.ai_model_service:check_compliance:1097 | JSON数据清理完成 | 请求ID: 63940a12-bdde-4769-9439-ab3089f835b2 | 清理后长度: 4116 字符
2025-08-12 15:02:08 | INFO     | app.services.ai_model_service:check_compliance:1104 | 开始解析清理后的JSON数据 | 请求ID: 63940a12-bdde-4769-9439-ab3089f835b2
2025-08-12 15:02:08 | INFO     | app.services.ai_model_service:check_compliance:1106 | JSON解析成功 | 请求ID: 63940a12-bdde-4769-9439-ab3089f835b2 | 数据类型: <class 'dict'>
2025-08-12 15:02:08 | INFO     | app.services.ai_model_service:check_compliance:1124 | 开始解析AI返回的检查结果，共 10 个项目
2025-08-12 15:02:08 | INFO     | app.services.ai_model_service:check_compliance:1180 | 合规性检查完成，发现 10 个问题
2025-08-12 15:02:08 | INFO     | app.services.compliance_service:ai_compliance_check_stage:195 | AI合规性检查完成 | ID: 63940a12-bdde-4769-9439-ab3089f835b2 | 发现问题: 10个
2025-08-12 15:02:08 | WARNING  | app.core.logger:record_stage_performance:204 | 性能警告 | 阶段: ai_model_call | ID: 63940a12-bdde-4769-9439-ab3089f835b2 | 耗时: 212.846秒 > 阈值: 180.0秒
2025-08-12 15:02:08 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | ai_model_call | ID: 63940a12-bdde-4769-9439-ab3089f835b2 | 状态: 成功 | 耗时: 212.846秒
2025-08-12 15:02:08 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | AI合规性检查阶段 | ID: 63940a12-bdde-4769-9439-ab3089f835b2 | 状态: 成功 | 耗时: 212.846秒
2025-08-12 15:02:08 | INFO     | app.services.compliance_service:execute_pipeline:423 | 步骤4: AI合规性检查完成 | ID: 63940a12-bdde-4769-9439-ab3089f835b2 | 结果数量: 10
2025-08-12 15:02:08 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 敏感词检测阶段 | ID: 63940a12-bdde-4769-9439-ab3089f835b2 | 详情: {}
2025-08-12 15:02:08 | INFO     | app.services.compliance_service:sensitive_word_check_stage:245 | 开始敏感词检测 | ID: 63940a12-bdde-4769-9439-ab3089f835b2
2025-08-12 15:02:08 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 敏感词服务健康检查 | ID: 63940a12-bdde-4769-9439-ab3089f835b2 | 详情: {}
2025-08-12 15:02:08 | INFO     | app.services.sensitive_word_service:check_health:122 | 检查敏感词服务健康状态: http://************:8087/health
2025-08-12 15:02:08 | INFO     | app.services.sensitive_word_service:check_health:132 | 敏感词服务健康状态: 健康
2025-08-12 15:02:08 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 敏感词服务健康检查 | ID: 63940a12-bdde-4769-9439-ab3089f835b2 | 状态: 成功 | 耗时: 0.027秒
2025-08-12 15:02:08 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 敏感词检测 | ID: 63940a12-bdde-4769-9439-ab3089f835b2 | 详情: {}
2025-08-12 15:02:08 | INFO     | app.services.sensitive_word_service:detect_sensitive_words:214 | 开始敏感词检测: 内容长度=36683, 政府采购=False
2025-08-12 15:02:08 | INFO     | app.services.sensitive_word_service:detect_sensitive_words:223 | 敏感词检测超时设置: 60.0秒 | ID: 63940a12-bdde-4769-9439-ab3089f835b2
2025-08-12 15:02:08 | INFO     | app.services.sensitive_word_service:_convert_to_target_format:376 | 敏感词转换完成: 原始3个，有效3个
2025-08-12 15:02:08 | INFO     | app.core.logger:log_api_call:170 | API调用 | 敏感词检测API | ID: 63940a12-bdde-4769-9439-ab3089f835b2 | 状态: 成功 | 耗时: 0.000秒
2025-08-12 15:02:08 | INFO     | app.services.sensitive_word_service:detect_sensitive_words:269 | 敏感词检测完成: 发现 3 个敏感词
2025-08-12 15:02:08 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 敏感词检测 | ID: 63940a12-bdde-4769-9439-ab3089f835b2 | 状态: 成功 | 耗时: 0.020秒
2025-08-12 15:02:08 | INFO     | app.services.compliance_service:sensitive_word_check_stage:252 | 敏感词检测完成 | ID: 63940a12-bdde-4769-9439-ab3089f835b2 | 发现敏感词: 3个
2025-08-12 15:02:08 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | sensitive_word | ID: 63940a12-bdde-4769-9439-ab3089f835b2 | 状态: 成功 | 耗时: 0.050秒
2025-08-12 15:02:08 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 敏感词检测阶段 | ID: 63940a12-bdde-4769-9439-ab3089f835b2 | 状态: 成功 | 耗时: 0.050秒
2025-08-12 15:02:08 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 结果聚合阶段 | ID: 63940a12-bdde-4769-9439-ab3089f835b2 | 详情: {}
2025-08-12 15:02:08 | INFO     | app.services.compliance_service:result_aggregation_stage:305 | 开始结果聚合 | ID: 63940a12-bdde-4769-9439-ab3089f835b2
2025-08-12 15:02:08 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 结果聚合 | ID: 63940a12-bdde-4769-9439-ab3089f835b2 | 详情: {}
2025-08-12 15:02:08 | INFO     | app.services.result_processor:aggregate_results:334 | 开始结果聚合: 敏感词3个，检查结果10个 | ID: 63940a12-bdde-4769-9439-ab3089f835b2
2025-08-12 15:02:08 | INFO     | app.services.result_processor:validate_sensitive_words:109 | 敏感词验证完成: 原始3个，有效3个
2025-08-12 15:02:08 | INFO     | app.services.result_processor:deduplicate_sensitive_words:245 | 敏感词去重完成: 原始3个，去重后3个
2025-08-12 15:02:08 | INFO     | app.services.result_processor:_verify_deduplication_integrity:954 | 去重完整性验证 | ID: 63940a12-bdde-4769-9439-ab3089f835b2 | 状态: 完美 | 输入: 3个 | 输出: 3个 | 出现次数变化: 50 -> 50
2025-08-12 15:02:08 | INFO     | app.services.result_processor:validate_check_results:199 | 检查结果验证完成: 原始10个，有效10个
2025-08-12 15:02:08 | INFO     | app.services.result_processor:prioritize_check_results:283 | 检查结果排序完成: 10个结果
2025-08-12 15:02:08 | INFO     | app.services.result_processor:_verify_final_integrity:754 | 完整性报告 | ID: 63940a12-bdde-4769-9439-ab3089f835b2 | 敏感词保留率: 100.0% | 检查结果保留率: 100.0%
2025-08-12 15:02:08 | INFO     | app.services.result_processor:aggregate_results:396 | 结果聚合完成: 敏感词3个，检查结果10个 | ID: 63940a12-bdde-4769-9439-ab3089f835b2
2025-08-12 15:02:08 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 结果聚合 | ID: 63940a12-bdde-4769-9439-ab3089f835b2 | 状态: 成功 | 耗时: 0.002秒
2025-08-12 15:02:08 | INFO     | app.services.result_processor:validate_final_response:490 | 响应验证完成: 敏感词3个，检查结果10个
2025-08-12 15:02:08 | INFO     | app.services.compliance_service:result_aggregation_stage:312 | 结果聚合完成 | ID: 63940a12-bdde-4769-9439-ab3089f835b2 | 敏感词: 3个 | 检查结果: 10个
2025-08-12 15:02:08 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | result_aggregation | ID: 63940a12-bdde-4769-9439-ab3089f835b2 | 状态: 成功 | 耗时: 0.005秒
2025-08-12 15:02:08 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 结果聚合阶段 | ID: 63940a12-bdde-4769-9439-ab3089f835b2 | 状态: 成功 | 耗时: 0.005秒
2025-08-12 15:02:08 | INFO     | app.services.compliance_service:_add_pipeline_metadata:538 | 流水线元数据 | ID: 63940a12-bdde-4769-9439-ab3089f835b2 | 处理时间: 214.468秒 | 降级使用: False | 敏感词数量: 3 | 检查结果数量: 10
2025-08-12 15:02:08 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | total_pipeline | ID: 63940a12-bdde-4769-9439-ab3089f835b2 | 状态: 成功 | 耗时: 214.468秒
2025-08-12 15:02:08 | INFO     | app.services.compliance_service:execute_pipeline:479 | 合规性检查流水线完成 | ID: 63940a12-bdde-4769-9439-ab3089f835b2 | 耗时: 214.468秒 | 降级: False
2025-08-12 15:02:08 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 完整合规性检查流水线 | ID: 63940a12-bdde-4769-9439-ab3089f835b2 | 状态: 成功 | 耗时: 214.469秒
2025-08-12 15:02:08 | INFO     | app.services.compliance_service:check_compliance:641 | 合规性检查服务完成 | ID: 63940a12-bdde-4769-9439-ab3089f835b2
2025-08-12 15:02:08 | INFO     | app.api.routes:check_compliance_simple:156 | 简化合规性检查完成 | ID: 63940a12-bdde-4769-9439-ab3089f835b2 | 文件: rBIKBmiatqqAXC4lABaSuc9ZQzU547.doc | 敏感词: 3个 | 检查结果: 10个
2025-08-12 15:02:08 | INFO     | app.middleware.logging:dispatch:48 | 请求完成 | ID: 63940a12-bdde-4769-9439-ab3089f835b2 | 状态码: 200 | 耗时: 215.584秒
2025-08-12 15:05:53 | INFO     | app.middleware.logging:dispatch:35 | 请求开始 | ID: f53f9488-96ca-43f3-b775-a37382064dba | 方法: POST | URL: http://*************:8088/api/v1/check-compliance-simple | 客户端IP: ********** | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/75.0.3770.142 Safari/537.36 Hutool
2025-08-12 15:05:53 | INFO     | app.middleware.validation:_validate_request:75 | 跳过中间件验证 | ID: f53f9488-96ca-43f3-b775-a37382064dba | 路径: /api/v1/check-compliance-simple
2025-08-12 15:05:53 | INFO     | app.api.routes:check_compliance_simple:125 | 开始简化合规性检查 | ID: f53f9488-96ca-43f3-b775-a37382064dba | URL: https://test-fds.xinecai.com/group1/M00/02/94/rBIKBWia58KAFoSOACHunfnncLU987.doc?token=13cb1246d3755ab6a319e43279db9986&ts=1754982352
2025-08-12 15:05:53 | INFO     | app.utils.file_info_utils:infer_file_info_from_url:154 | 开始从URL推断文件信息: https://test-fds.xinecai.com/group1/M00/02/94/rBIKBWia58KAFoSOACHunfnncLU987.doc?token=13cb1246d3755ab6a319e43279db9986&ts=1754982352
2025-08-12 15:05:53 | INFO     | app.utils.file_info_utils:infer_file_info_from_url:158 | 推断的文件名: rBIKBWia58KAFoSOACHunfnncLU987.doc
2025-08-12 15:05:53 | WARNING  | app.utils.file_info_utils:infer_file_extension_and_mime_type:107 | 未识别的文件扩展名: .doc，默认使用docx格式
2025-08-12 15:05:53 | INFO     | app.utils.file_info_utils:infer_file_info_from_url:162 | 推断的扩展名: .docx, MIME类型: application/vnd.openxmlformats-officedocument.wordprocessingml.document
2025-08-12 15:05:55 | INFO     | app.utils.file_info_utils:infer_file_info_from_url:171 | 获取的文件大小: 2223773 字节
2025-08-12 15:05:55 | INFO     | app.utils.file_info_utils:infer_file_info_from_url:182 | 文件信息推断完成: rBIKBWia58KAFoSOACHunfnncLU987.doc
2025-08-12 15:05:55 | INFO     | app.api.routes:check_compliance_simple:131 | 文件信息推断成功 | ID: f53f9488-96ca-43f3-b775-a37382064dba | 文件: rBIKBWia58KAFoSOACHunfnncLU987.doc
2025-08-12 15:05:55 | INFO     | app.services.compliance_service:check_compliance:636 | 合规性检查服务开始 | ID: f53f9488-96ca-43f3-b775-a37382064dba
2025-08-12 15:05:55 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 完整合规性检查流水线 | ID: f53f9488-96ca-43f3-b775-a37382064dba | 详情: {}
2025-08-12 15:05:55 | INFO     | app.services.compliance_service:execute_pipeline:376 | 开始执行合规性检查流水线 | ID: f53f9488-96ca-43f3-b775-a37382064dba | 文件: rBIKBWia58KAFoSOACHunfnncLU987.doc
2025-08-12 15:05:55 | INFO     | app.services.compliance_service:execute_pipeline:386 | 步骤1: 开始验证服务前置条件 | ID: f53f9488-96ca-43f3-b775-a37382064dba
2025-08-12 15:05:55 | INFO     | app.services.compliance_service:validate_prerequisites:73 | 开始验证服务前置条件 | ID: f53f9488-96ca-43f3-b775-a37382064dba
2025-08-12 15:05:55 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 敏感词服务健康检查 | ID: f53f9488-96ca-43f3-b775-a37382064dba | 详情: {}
2025-08-12 15:05:55 | INFO     | app.services.sensitive_word_service:check_health:122 | 检查敏感词服务健康状态: http://************:8087/health
2025-08-12 15:05:55 | INFO     | app.services.sensitive_word_service:check_health:132 | 敏感词服务健康状态: 健康
2025-08-12 15:05:55 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 敏感词服务健康检查 | ID: f53f9488-96ca-43f3-b775-a37382064dba | 状态: 成功 | 耗时: 0.020秒
2025-08-12 15:05:55 | INFO     | app.services.compliance_service:validate_prerequisites:92 | 服务健康检查完成 | ID: f53f9488-96ca-43f3-b775-a37382064dba | 状态: {'file_processor': True, 'ai_model': True, 'sensitive_word': True, 'result_processor': True}
2025-08-12 15:05:55 | INFO     | app.services.compliance_service:execute_pipeline:388 | 步骤1: 服务前置条件验证完成 | ID: f53f9488-96ca-43f3-b775-a37382064dba | 状态: {'file_processor': True, 'ai_model': True, 'sensitive_word': True, 'result_processor': True}
2025-08-12 15:05:55 | INFO     | app.services.compliance_service:execute_pipeline:394 | 步骤2: 开始获取项目信息 | ID: f53f9488-96ca-43f3-b775-a37382064dba
2025-08-12 15:05:55 | INFO     | app.services.compliance_service:execute_pipeline:396 | 步骤2: 项目信息获取完成 | ID: f53f9488-96ca-43f3-b775-a37382064dba
2025-08-12 15:05:55 | INFO     | app.services.compliance_service:execute_pipeline:400 | 步骤3: 开始文件处理阶段 | ID: f53f9488-96ca-43f3-b775-a37382064dba
2025-08-12 15:05:55 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 文件处理阶段 | ID: f53f9488-96ca-43f3-b775-a37382064dba | 详情: {}
2025-08-12 15:05:55 | INFO     | app.services.compliance_service:process_file_stage:114 | 开始文件处理阶段 | ID: f53f9488-96ca-43f3-b775-a37382064dba | 文件: rBIKBWia58KAFoSOACHunfnncLU987.doc
2025-08-12 15:05:55 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 优化文件处理流程 | ID: f53f9488-96ca-43f3-b775-a37382064dba | 详情: {}
2025-08-12 15:05:55 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 文件下载 | ID: f53f9488-96ca-43f3-b775-a37382064dba | 详情: {}
2025-08-12 15:05:55 | INFO     | app.services.file_processor_v2:download_file:255 | 开始下载文件: https://test-fds.xinecai.com/group1/M00/02/94/rBIKBWia58KAFoSOACHunfnncLU987.doc?token=13cb1246d3755ab6a319e43279db9986&ts=1754982352
2025-08-12 15:06:03 | INFO     | app.services.file_processor_v2:download_file:289 | 文件下载完成: 2223773 字节
2025-08-12 15:06:03 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 文件下载 | ID: f53f9488-96ca-43f3-b775-a37382064dba | 状态: 成功 | 耗时: 7.736秒
2025-08-12 15:06:03 | WARNING  | app.services.file_processor_v2:process_file:515 | MarkItDown不可用，使用备用方案
2025-08-12 15:06:03 | WARNING  | app.services.file_processor_v2:process_file:519 | MarkItDown处理失败: MarkItDown不可用，使用备用方案
2025-08-12 15:06:03 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | DOCX备用提取 | ID: f53f9488-96ca-43f3-b775-a37382064dba | 详情: {}
2025-08-12 15:06:03 | INFO     | app.services.file_processor_v2:extract_docx_content_fallback:401 | DOCX备用提取完成: 60381 字符
2025-08-12 15:06:03 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | DOCX备用提取 | ID: f53f9488-96ca-43f3-b775-a37382064dba | 状态: 成功 | 耗时: 0.212秒
2025-08-12 15:06:03 | INFO     | app.core.logger:log_file_processing:159 | 文件处理 | ID: f53f9488-96ca-43f3-b775-a37382064dba | 文件: rBIKBWia58KAFoSOACHunfnncLU987.doc | 大小: 2223773 字节 | 处理时间: 0.000秒
2025-08-12 15:06:03 | INFO     | app.services.file_processor_v2:process_file:548 | 文件处理完成(备用方案): rBIKBWia58KAFoSOACHunfnncLU987.doc, 输出长度: 60441 字符
2025-08-12 15:06:03 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 优化文件处理流程 | ID: f53f9488-96ca-43f3-b775-a37382064dba | 状态: 成功 | 耗时: 7.951秒
2025-08-12 15:06:03 | INFO     | app.services.compliance_service:process_file_stage:131 | 文件处理完成 | ID: f53f9488-96ca-43f3-b775-a37382064dba | 内容长度: 60441 字符
2025-08-12 15:06:03 | INFO     | app.core.logger:log_file_processing:159 | 文件处理 | ID: f53f9488-96ca-43f3-b775-a37382064dba | 文件: rBIKBWia58KAFoSOACHunfnncLU987.doc | 大小: 2223773 字节 | 处理时间: 0.000秒
2025-08-12 15:06:03 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | file_processing | ID: f53f9488-96ca-43f3-b775-a37382064dba | 状态: 成功 | 耗时: 7.952秒
2025-08-12 15:06:03 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 文件处理阶段 | ID: f53f9488-96ca-43f3-b775-a37382064dba | 状态: 成功 | 耗时: 7.952秒
2025-08-12 15:06:03 | INFO     | app.services.compliance_service:execute_pipeline:405 | 步骤3: 文件处理完成 | ID: f53f9488-96ca-43f3-b775-a37382064dba | 内容长度: 60441
2025-08-12 15:06:03 | INFO     | app.services.compliance_service:execute_pipeline:416 | 步骤4: 开始AI合规性检查阶段 | ID: f53f9488-96ca-43f3-b775-a37382064dba
2025-08-12 15:06:03 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | AI合规性检查阶段 | ID: f53f9488-96ca-43f3-b775-a37382064dba | 详情: {}
2025-08-12 15:06:03 | INFO     | app.services.compliance_service:ai_compliance_check_stage:188 | 开始AI合规性检查 | ID: f53f9488-96ca-43f3-b775-a37382064dba
2025-08-12 15:06:03 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | AI模型调用(尝试1) | ID: f53f9488-96ca-43f3-b775-a37382064dba | 详情: {}
2025-08-12 15:06:03 | INFO     | app.services.ai_model_service:_call_model_with_retry:770 | 调用AI模型: gemini-2.5-flash, 消息数: 2, 尝试: 1/4 | 请求ID: f53f9488-96ca-43f3-b775-a37382064dba
2025-08-12 15:07:17 | INFO     | app.core.logger:log_api_call:170 | API调用 | OpenAI-gemini-2.5-flash | ID: f53f9488-96ca-43f3-b775-a37382064dba | 状态: 成功 | 耗时: 0.000秒
2025-08-12 15:07:17 | INFO     | app.services.ai_model_service:_call_model_with_retry:809 | AI模型调用成功: 响应长度 6112 字符, 尝试次数: 1 | 请求ID: f53f9488-96ca-43f3-b775-a37382064dba
2025-08-12 15:07:17 | WARNING  | app.services.ai_model_service:_call_model_with_retry:813 | AI模型原始响应内容: '```json
{
  "checkResultArr": [
    {
      "quesType": "规范性",
      "quesDesc": "招标文件标题和项目名称中存在多余文字，不严谨。",
      "originalArr": [
        "安徽理工大学第一附属医院南区污水医废、生活垃圾一体化托管服务项目二次 招标文件阿斯顿发斯蒂芬",
        "项目名称：安徽理工大学第一附属医院南区污水医废、生活垃圾一体化托管服务项目二次阿萨德"
      ],
      "point": "文本准确性",
      "advice": "删除招标文件标题和项目名称中多余的“阿斯顿发斯蒂芬”和“阿萨德”字样，确保文本准确无误。"
    },
    {
      "quesType": "合规性",
      "quesDesc": "投标人资格要求中多项仅要求提供承诺函，未要求提供证明材料，可能导致无法有效核实投标人资格。",
      "originalArr": [
        "2.2具有良好的商业信誉和健全的财务会计制度；",
        "2.3具有履行合同所必需的设备和专业技术能力；",
        "2.4有依法缴纳税收和社会保障资金的良好记录；",
        "2.5参加医院采购活动前三年内，在经营活动中没有重大违法记录。（提供承诺函并加盖公章）",
        "3.(4)近三年内（自开标之日向前追溯3年）投标人或其法定代表人被人民法院判处行贿罪或被人民检察院/中华人民共和国国家监察委员会列入行贿犯罪档案的（投标人须自行承诺并加盖公章，承诺书内容及格式自拟）"
      ],
      "point": "投标人资格要求",
      "advice": "对于依法招标项目，建议在要求提供承诺函的同时，明确要求投标人提供相应的证明材料，如近期的财务报表或审计报告、社保缴纳记录、信用查询报告等，以确保资格审查的严谨性和合规性。"
    },
    {
      "quesType": "可操作性",
      "quesDesc": "投标人须知中“近X年内”的“X”未明确具体年限，存在歧义。",
      "originalArr": [
        "2.5近X年内：按招标文件要求，没作要求的为自开标之日往前追溯X年。"
      ],
      "point": "文本准确性",
      "advice": "明确“近X年内”的具体年限，例如“近3年内”或“近5年内”，以消除歧义，方便投标人理解和操作。"
    },
    {
      "quesType": "逻辑性",
      "quesDesc": "投标人须知中关于联合体投标的通用条款与招标公告及投标人须知前附表明确不接受联合体投标的规定相冲突。",
      "originalArr": [
        "8.1除非本项目明确要求不接受联合体形式投标外，两个或两个以上投标人可以组成一个联合体投标，以一个投标人的身份投标。"
      ],
      "point": "条款一致性",
      "advice": "鉴于招标公告和投标人须知前附表已明确本项目不接受联合体投标，建议删除或修改投标人须知中与此相悖的通用条款，以保持文件内部逻辑一致性。"
    },
    {
      "quesType": "文本准确性",
      "quesDesc": "本项目为服务类项目，但将其描述为“交钥匙工程”不准确。",
      "originalArr": [
        "15.8本项目为交钥匙工程，报价应包括所有发生的全部费用。除政策性文件规定以外，投标人所报价格在合同实施期间不因市场变化因素而变动。"
      ],
      "point": "文本准确性",
      "advice": "“交钥匙工程”通常用于工程项目，建议修改此表述，使用更符合服务类项目特点的描述，如“本项目为一体化托管服务，报价应包含所有服务内容及相关费用”。"
    },
    {
      "quesType": "逻辑性",
      "quesDesc": "关于电子投标文件解密失败补救措施的条款在不同章节中存在矛盾。",
      "originalArr": [
        "第三章 投标人须知 23.5 电子投标文件解密失败的补救措施：见投标人须知前附表。",
        "第八章 电子招投标相关要求 31 是否允许采用补救措施：■不允许采用补救措施。"
      ],
      "point": "上下文一致性",
      "advice": "明确电子投标文件解密失败是否允许采用补救措施，并确保所有相关条款表述一致，避免歧义。"
    },
    {
      "quesType": "合规性",
      "quesDesc": "公开招标项目在投标人不足三家时，招标文件允许转为磋商或两家继续评审，不符合《中华人民共和国招标投标法实施条例》的规定。",
      "originalArr": [
        "第三章 投标人须知 23.8 (1) 投标人不足三家处理预案。发布过二次招标公告或二次招标（重新招标）的项目，在投标文件递交截止时间后出现投标人不足三家情形的，为保证本项目的采购进度，经现场投标人、招标人同意后，可采用磋商方式继续采购，其投标保证金转为磋商保证金。资格性审查或符合性审查不合格的投标人不得参加磋商。",
        "第三章 投标人须知 23.8 (2) 对于未发布或发布过二次招标公告的项目（非政府采购项目）如果评标期间出现符合资格条件的投标人或者对招标文件作实质响应的投标人不足三家情形的，如有两家有效投标人进入最终详细评审，且评标委员会认为具有一定的竞争性的，将按照招标文件规定的评审办法进行评审；对于未发布过二次招标公告的项目（非政府采购项目）如进入最终详细评审的投标人只有一家，招标人将进行重新招标。"
      ],
      "point": "合规性",
      "advice": "根据《中华人民共和国招标投标法实施条例》第四十四条，公开招标项目投标人少于三个的，招标人应当重新招标。建议删除或修改此条款，确保招标程序符合法律法规要求。"
    },
    {
      "quesType": "公平性",
      "quesDesc": "无效投标和串通投标的认定标准中包含主观性描述，缺乏客观量化标准，可能导致评标过程中的不公平性。",
      "originalArr": [
        "第三章 投标人须知 25.1 (3) 未按规定的格式填写，编排混乱、无序，内容不全或字迹模糊，辨认不清；",
        "第三章 投标人须知 25.1 (6) 其他被评委会认定无效的情况。",
        "第三章 投标人须知 25.2 (14) 不同投标人的投标文件以及密封包装出现了评标委员会认为不应当雷同的情况；"
      ],
      "point": "评分合规性",
      "advice": "细化无效投标和串通投标的认定标准，用客观、量化的语言描述，避免使用“编排混乱、无序”、“辨认不清”、“其他被评委会认定无效的情况”和“评标委员会认为不应当雷同的情况”等主观性词语，以确保评标的公平公正。"
    },
    {
      "quesType": "逻辑性",
      "quesDesc": "关于履约保证金的要求在不同章节中存在矛盾。",
      "originalArr": [
        "第三章 投标人须知 27.5 中标人放弃中标资格、因不可抗力不能履行合同、不按照招标文件要求提交履约保证金，或者被查实存在影响中标结果的违法行为等情形，不符合中标条件的，招标人可以按照评标委员会提出的中标候选人名单排序依次确定其他中标候选人为中标人，也可以重新招标。",
        "第八章 电子招投标相关要求 47 履约保证金：■不要求"
      ],
      "point": "上下文一致性",
      "advice": "明确本项目是否要求履约保证金。如果不需要，则删除第三章中提及履约保证金的条款；如果需要，则在第八章中明确要求并补充相关细则，确保文件内部一致性。"
    },
    {
      "quesType": "可操作性",
      "quesDesc": "污泥处置费用支付方式的表述可能引起歧义，影响投标人报价。",
      "originalArr": [
        "第四章 服务要求 (一) 2.2.3 污水处理站污泥处置要求：...涉及危险废物的处置费用由运维单位代为支付。"
      ],
      "point": "风险管理",
      "advice": "明确污泥处置费用是否已包含在投标总价中，或由采购人另行支付。建议统一表述为“涉及危险废物的处置费用由中标单位承担并包含在投标总价中”，以避免后续争议。"
    },
    {
      "quesType": "公平性",
      "quesDesc": "要求服务人员提供最近3个月社保证明并要求为“正式员工”，可能构成对潜在投标人的不合理限制。",
      "originalArr": [
        "第四章 服务要求 (一) 3.16 注：本条款所涉及人员，供应商须提供其相关证件扫描件及为其缴纳的最近3个月社保证明材料，未提供视为不响应文件，按无效响应处理。",
        "第四章 服务要求 (一) 4.4 ...且为成交供应商正式员工，须熟练掌握设备的操作流程，严格按规范操作；注：本条款所涉及人员，供应商须提供其相关证件扫描件及为其缴纳的最近3个月社保证明材料，未提供视为不响应文件，按无效响应处理。",
        "第五章 评标方法 评审因素-项目管理人员 2、投标人拟派服务本项目人员需为投标投标人正式员工，提供本单位自2024年9月份以来为其缴纳的任意连续三个月的社保证明（含人社部门官网在线打印件）否则不予计分。"
      ],
      "point": "投标人资格要求",
      "advice": "社保证明和“正式员工”的要求可能限制了新入职员工或劳务派遣人员的参与，涉嫌以不合理的条件限制、排斥潜在投标人，不符合《中华人民共和国招标投标法实施条例》第三十八条规定。建议取消或放宽此要求，或允许提供其他证明其稳定劳动关系的材料。"
    },
    {
      "quesType": "公平性",
      "quesDesc": "投标人业绩要求中限定为“三级甲等医院”的类似业绩，可能对潜在投标人构成不合理的限制。",
      "originalArr": [
        "第五章 评标方法 评审因素-投标人业绩 自2022年1月1日以来（以合同签订时间为准），投标人具有三级甲等医院污水处理站托管运维类似业绩，每提供一份加3分，满分12分。"
      ],
      "point": "投标人资格要求",
      "advice": "建议取消或放宽对医院等级的限制，允许提供同等规模或类似性质的其他医疗机构或企事业单位的污水处理站托管运维业绩，以扩大竞争范围，促进公平竞争。"
    },
    {
      "quesType": "风险管理",
      "quesDesc": "将生成投标文件的硬件信息异常一致作为无效投标的依据，可能存在争议和误判风险。",
      "originalArr": [
        "第八章 电子招投标相关要求 51.3 (1) 本项目评审时将查询生成投标文件的硬件信息，如不同投标文件的硬件信息异常一致，相关投标将被认定为投标无效。"
      ],
      "point": "公平性",
      "advice": "硬件信息异常一致不必然代表串通投标，可能因使用相同软件或制作环境导致。建议谨慎使用此条款作为无效投标的直接依据，或补充更明确的串通投标认定标准，避免误判，保障投标人合法权益。"
    },
    {
      "quesType": "公平性",
      "quesDesc": "将投标报价降幅过小、缺乏竞争性作为围标、串标的嫌疑认定标准，属于主观判断，缺乏客观量化依据。",
      "originalArr": [
        "第八章 电子招投标相关要求 51.4 (12) 投标人投标报价与公布的最高投标限价（控制价）相比降幅过小，明显缺乏竞争性，有围标、串标嫌疑的。"
      ],
      "point": "评分合规性",
      "advice": "此条款过于主观，可能导致不公平的废标。建议删除此条款，或补充更客观、量化的围标、串标认定标准，避免主观臆断。"
    },
    {
      "quesType": "合规性",
      "quesDesc": "异议渠道仅规定向代理机构提出，未明确招标人作为异议受理主体，且限制了部分异议内容，不符合法律规定。",
      "originalArr": [
        "第八章 电子招投标相关要求 51.5 2.对评标结果的异议：招标投标相关各方对评标结果有异议的，可在公示期内以书面形式向代理机构提出。",
        "第八章 电子招投标相关要求 51.5 5.异议渠道：①通过安天智采招标采购电子交易平台（https://www.xinecai.com/）在线向代理机构提出；②提供书面材料，递交至代理机构处。",
        "第八章 电子招投标相关要求 51.5 4.异议材料有下列情形之一，不予受理：...④对其他投标人的投标文件详细内容异议，无法提供合法来源渠道的；"
      ],
      "point": "风险管理",
      "advice": "根据《中华人民共和国招标投标法实施条例》第六十条，投标人或者其他利害关系人对评标结果有异议的，应当在中标候选人公示期间向招标人提出。建议明确招标人作为异议受理主体，并允许投标人对其他投标文件的合法合规性提出异议，保障投标人合法权利。"
    }
  ]
}
```'
2025-08-12 15:07:17 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | AI模型调用(尝试1) | ID: f53f9488-96ca-43f3-b775-a37382064dba | 状态: 成功 | 耗时: 74.365秒
2025-08-12 15:07:17 | INFO     | app.services.ai_model_service:check_compliance:1090 | 开始清理AI响应JSON数据 | 请求ID: f53f9488-96ca-43f3-b775-a37382064dba | 原始长度: 6112 字符
2025-08-12 15:07:17 | WARNING  | app.services.ai_model_service:clean_json_data:394 | 第一次JSON解析失败，尝试深度修复: Expecting ',' delimiter: line 1 column 5655 (char 5654)
2025-08-12 15:07:17 | WARNING  | app.services.ai_model_service:clean_json_data:395 | 第一次解析失败的内容: {  "checkResultArr": [    {      "quesType": "规范性",      "quesDesc": "招标文件标题和项目名称中存在多余文字，不严谨。",      "originalArr": [        "安徽理工大学第一附属医院南区污水医废、生活垃圾一体化托管服务项目二次 招标文件阿斯顿发斯蒂芬",        "项目名称：安徽理工大学第一附属医院南区污水医废、生活垃圾一体化托管服务项目二次阿萨德"      ],      "point": "文本准确性",      "advice": "删除招标文件标题和项目名称中多余的“阿斯顿发斯蒂芬”和“阿萨德”字样，确保文本准确无误。"    },    {      "quesType": "合规性",      "quesDesc": "投标人资格要求中多项仅要求提供承诺函，未要求提供证明材料，可能导致无法有效核实投标人资格。",      "originalArr": [        "2.2具有良好的商业信誉和健全的财务会计制度；",        "2.3具有履行合同所必需的设备...
2025-08-12 15:07:17 | WARNING  | app.services.ai_model_service:clean_json_data:401 | 提取JSON后的内容: {  "checkResultArr": [    {      "quesType": "规范性",      "quesDesc": "招标文件标题和项目名称中存在多余文字，不严谨。",      "originalArr": [        "安徽理工大学第一附属医院南区污水医废、生活垃圾一体化托管服务项目二次 招标文件阿斯顿发斯蒂芬",        "项目名称：安徽理工大学第一附属医院南区污水医废、生活垃圾一体化托管服务项目二次阿萨德"      ],      "point": "文本准确性",      "advice": "删除招标文件标题和项目名称中多余的“阿斯顿发斯蒂芬”和“阿萨德”字样，确保文本准确无误。"    },    {      "quesType": "合规性",      "quesDesc": "投标人资格要求中多项仅要求提供承诺函，未要求提供证明材料，可能导致无法有效核实投标人资格。",      "originalArr": [        "2.2具有良好的商业信誉和健全的财务会计制度；",        "2.3具有履行合同所必需的设备...
2025-08-12 15:07:17 | WARNING  | app.services.ai_model_service:clean_json_data:404 | 尝试修复JSON引号问题，错误位置: 第5655列
2025-08-12 15:07:17 | WARNING  | app.services.ai_model_service:clean_json_data:411 | 全面JSON修复成功
2025-08-12 15:07:17 | INFO     | app.services.ai_model_service:check_compliance:1097 | JSON数据清理完成 | 请求ID: f53f9488-96ca-43f3-b775-a37382064dba | 清理后长度: 5955 字符
2025-08-12 15:07:17 | INFO     | app.services.ai_model_service:check_compliance:1104 | 开始解析清理后的JSON数据 | 请求ID: f53f9488-96ca-43f3-b775-a37382064dba
2025-08-12 15:07:17 | INFO     | app.services.ai_model_service:check_compliance:1106 | JSON解析成功 | 请求ID: f53f9488-96ca-43f3-b775-a37382064dba | 数据类型: <class 'dict'>
2025-08-12 15:07:17 | INFO     | app.services.ai_model_service:check_compliance:1124 | 开始解析AI返回的检查结果，共 15 个项目
2025-08-12 15:07:17 | WARNING  | app.services.ai_model_service:check_compliance:1156 | 无效的问题类型: '文本准确性', 使用默认值 '规范性'
2025-08-12 15:07:17 | INFO     | app.services.ai_model_service:check_compliance:1180 | 合规性检查完成，发现 15 个问题
2025-08-12 15:07:17 | INFO     | app.services.compliance_service:ai_compliance_check_stage:195 | AI合规性检查完成 | ID: f53f9488-96ca-43f3-b775-a37382064dba | 发现问题: 15个
2025-08-12 15:07:17 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | ai_model_call | ID: f53f9488-96ca-43f3-b775-a37382064dba | 状态: 成功 | 耗时: 74.380秒
2025-08-12 15:07:17 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | AI合规性检查阶段 | ID: f53f9488-96ca-43f3-b775-a37382064dba | 状态: 成功 | 耗时: 74.380秒
2025-08-12 15:07:17 | INFO     | app.services.compliance_service:execute_pipeline:423 | 步骤4: AI合规性检查完成 | ID: f53f9488-96ca-43f3-b775-a37382064dba | 结果数量: 15
2025-08-12 15:07:17 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 敏感词检测阶段 | ID: f53f9488-96ca-43f3-b775-a37382064dba | 详情: {}
2025-08-12 15:07:17 | INFO     | app.services.compliance_service:sensitive_word_check_stage:245 | 开始敏感词检测 | ID: f53f9488-96ca-43f3-b775-a37382064dba
2025-08-12 15:07:17 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 敏感词服务健康检查 | ID: f53f9488-96ca-43f3-b775-a37382064dba | 详情: {}
2025-08-12 15:07:17 | INFO     | app.services.sensitive_word_service:check_health:122 | 检查敏感词服务健康状态: http://************:8087/health
2025-08-12 15:07:17 | INFO     | app.services.sensitive_word_service:check_health:132 | 敏感词服务健康状态: 健康
2025-08-12 15:07:17 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 敏感词服务健康检查 | ID: f53f9488-96ca-43f3-b775-a37382064dba | 状态: 成功 | 耗时: 0.024秒
2025-08-12 15:07:17 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 敏感词检测 | ID: f53f9488-96ca-43f3-b775-a37382064dba | 详情: {}
2025-08-12 15:07:17 | INFO     | app.services.sensitive_word_service:detect_sensitive_words:214 | 开始敏感词检测: 内容长度=60441, 政府采购=False
2025-08-12 15:07:17 | INFO     | app.services.sensitive_word_service:detect_sensitive_words:223 | 敏感词检测超时设置: 60.0秒 | ID: f53f9488-96ca-43f3-b775-a37382064dba
2025-08-12 15:07:17 | INFO     | app.services.sensitive_word_service:_convert_to_target_format:376 | 敏感词转换完成: 原始5个，有效5个
2025-08-12 15:07:17 | INFO     | app.core.logger:log_api_call:170 | API调用 | 敏感词检测API | ID: f53f9488-96ca-43f3-b775-a37382064dba | 状态: 成功 | 耗时: 0.000秒
2025-08-12 15:07:17 | INFO     | app.services.sensitive_word_service:detect_sensitive_words:269 | 敏感词检测完成: 发现 5 个敏感词
2025-08-12 15:07:17 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 敏感词检测 | ID: f53f9488-96ca-43f3-b775-a37382064dba | 状态: 成功 | 耗时: 0.019秒
2025-08-12 15:07:17 | INFO     | app.services.compliance_service:sensitive_word_check_stage:252 | 敏感词检测完成 | ID: f53f9488-96ca-43f3-b775-a37382064dba | 发现敏感词: 5个
2025-08-12 15:07:17 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | sensitive_word | ID: f53f9488-96ca-43f3-b775-a37382064dba | 状态: 成功 | 耗时: 0.046秒
2025-08-12 15:07:17 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 敏感词检测阶段 | ID: f53f9488-96ca-43f3-b775-a37382064dba | 状态: 成功 | 耗时: 0.046秒
2025-08-12 15:07:17 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 结果聚合阶段 | ID: f53f9488-96ca-43f3-b775-a37382064dba | 详情: {}
2025-08-12 15:07:17 | INFO     | app.services.compliance_service:result_aggregation_stage:305 | 开始结果聚合 | ID: f53f9488-96ca-43f3-b775-a37382064dba
2025-08-12 15:07:17 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 结果聚合 | ID: f53f9488-96ca-43f3-b775-a37382064dba | 详情: {}
2025-08-12 15:07:17 | INFO     | app.services.result_processor:aggregate_results:334 | 开始结果聚合: 敏感词5个，检查结果15个 | ID: f53f9488-96ca-43f3-b775-a37382064dba
2025-08-12 15:07:17 | INFO     | app.services.result_processor:validate_sensitive_words:109 | 敏感词验证完成: 原始5个，有效5个
2025-08-12 15:07:17 | INFO     | app.services.result_processor:deduplicate_sensitive_words:245 | 敏感词去重完成: 原始5个，去重后5个
2025-08-12 15:07:17 | INFO     | app.services.result_processor:_verify_deduplication_integrity:954 | 去重完整性验证 | ID: f53f9488-96ca-43f3-b775-a37382064dba | 状态: 完美 | 输入: 5个 | 输出: 5个 | 出现次数变化: 118 -> 118
2025-08-12 15:07:17 | INFO     | app.services.result_processor:validate_check_results:199 | 检查结果验证完成: 原始15个，有效15个
2025-08-12 15:07:17 | INFO     | app.services.result_processor:prioritize_check_results:283 | 检查结果排序完成: 15个结果
2025-08-12 15:07:17 | INFO     | app.services.result_processor:_verify_final_integrity:754 | 完整性报告 | ID: f53f9488-96ca-43f3-b775-a37382064dba | 敏感词保留率: 100.0% | 检查结果保留率: 100.0%
2025-08-12 15:07:17 | INFO     | app.services.result_processor:aggregate_results:396 | 结果聚合完成: 敏感词5个，检查结果15个 | ID: f53f9488-96ca-43f3-b775-a37382064dba
2025-08-12 15:07:17 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 结果聚合 | ID: f53f9488-96ca-43f3-b775-a37382064dba | 状态: 成功 | 耗时: 0.004秒
2025-08-12 15:07:17 | INFO     | app.services.result_processor:validate_final_response:490 | 响应验证完成: 敏感词5个，检查结果15个
2025-08-12 15:07:17 | INFO     | app.services.compliance_service:result_aggregation_stage:312 | 结果聚合完成 | ID: f53f9488-96ca-43f3-b775-a37382064dba | 敏感词: 5个 | 检查结果: 15个
2025-08-12 15:07:17 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | result_aggregation | ID: f53f9488-96ca-43f3-b775-a37382064dba | 状态: 成功 | 耗时: 0.006秒
2025-08-12 15:07:17 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 结果聚合阶段 | ID: f53f9488-96ca-43f3-b775-a37382064dba | 状态: 成功 | 耗时: 0.006秒
2025-08-12 15:07:17 | INFO     | app.services.compliance_service:_add_pipeline_metadata:538 | 流水线元数据 | ID: f53f9488-96ca-43f3-b775-a37382064dba | 处理时间: 82.411秒 | 降级使用: False | 敏感词数量: 5 | 检查结果数量: 15
2025-08-12 15:07:17 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | total_pipeline | ID: f53f9488-96ca-43f3-b775-a37382064dba | 状态: 成功 | 耗时: 82.411秒
2025-08-12 15:07:17 | INFO     | app.services.compliance_service:execute_pipeline:479 | 合规性检查流水线完成 | ID: f53f9488-96ca-43f3-b775-a37382064dba | 耗时: 82.411秒 | 降级: False
2025-08-12 15:07:17 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 完整合规性检查流水线 | ID: f53f9488-96ca-43f3-b775-a37382064dba | 状态: 成功 | 耗时: 82.412秒
2025-08-12 15:07:17 | INFO     | app.services.compliance_service:check_compliance:641 | 合规性检查服务完成 | ID: f53f9488-96ca-43f3-b775-a37382064dba
2025-08-12 15:07:17 | INFO     | app.api.routes:check_compliance_simple:156 | 简化合规性检查完成 | ID: f53f9488-96ca-43f3-b775-a37382064dba | 文件: rBIKBWia58KAFoSOACHunfnncLU987.doc | 敏感词: 5个 | 检查结果: 15个
2025-08-12 15:07:17 | INFO     | app.middleware.logging:dispatch:48 | 请求完成 | ID: f53f9488-96ca-43f3-b775-a37382064dba | 状态码: 200 | 耗时: 84.138秒
2025-08-12 16:16:46 | INFO     | main:lifespan:47 | 招标文件合规性检查助手关闭中...
2025-08-12 16:16:46 | INFO     | main:lifespan:51 | 停止异步请求队列...
2025-08-12 16:16:46 | INFO     | app.core.queue_manager:stop_workers:125 | 停止请求队列工作线程
2025-08-12 16:16:46 | INFO     | app.core.queue_manager:_worker:296 | 工作线程被取消: worker-0
2025-08-12 16:16:46 | INFO     | app.core.queue_manager:_worker:301 | 工作线程停止: worker-0
2025-08-12 16:16:46 | INFO     | app.core.queue_manager:_worker:296 | 工作线程被取消: worker-1
2025-08-12 16:16:46 | INFO     | app.core.queue_manager:_worker:301 | 工作线程停止: worker-1
2025-08-12 16:16:46 | INFO     | app.core.queue_manager:_worker:296 | 工作线程被取消: worker-2
2025-08-12 16:16:46 | INFO     | app.core.queue_manager:_worker:301 | 工作线程停止: worker-2
2025-08-12 16:16:46 | INFO     | app.core.queue_manager:_worker:296 | 工作线程被取消: worker-3
2025-08-12 16:16:46 | INFO     | app.core.queue_manager:_worker:301 | 工作线程停止: worker-3
2025-08-12 16:16:46 | INFO     | main:lifespan:53 | 请求队列已停止
2025-08-12 16:16:46 | INFO     | app.core.cache_manager:clear:198 | 缓存已清空
2025-08-12 16:16:46 | INFO     | main:lifespan:58 | 缓存已清理
2025-08-12 16:16:46 | INFO     | main:lifespan:60 | 招标文件合规性检查助手已关闭
2025-08-12 16:43:47 | INFO     | app.services.ai_model_service:_initialize_client:80 | AI模型客户端初始化成功: gemini-2.5-flash
2025-08-12 16:43:49 | INFO     | app.services.ai_model_service:_initialize_client:80 | AI模型客户端初始化成功: gemini-2.5-flash
2025-08-12 16:43:50 | INFO     | main:lifespan:26 | 招标文件合规性检查助手启动中...
2025-08-12 16:43:50 | INFO     | main:lifespan:27 | 配置信息: 模型=gemini-2.5-flash, 环境=development
2025-08-12 16:43:50 | INFO     | main:lifespan:31 | 启动异步请求队列...
2025-08-12 16:43:50 | INFO     | app.core.queue_manager:start_workers:117 | 启动请求队列工作线程: 4个
2025-08-12 16:43:50 | INFO     | main:lifespan:33 | 请求队列已启动，工作线程数: 4
2025-08-12 16:43:50 | INFO     | main:lifespan:37 | 文档缓存已启用，最大大小: 100MB
2025-08-12 16:43:50 | INFO     | main:lifespan:40 | 内存阈值: 2048MB, 自动GC: True
2025-08-12 16:43:50 | INFO     | app.core.queue_manager:_worker:266 | 工作线程启动: worker-0
2025-08-12 16:43:50 | INFO     | app.core.queue_manager:_worker:266 | 工作线程启动: worker-1
2025-08-12 16:43:50 | INFO     | app.core.queue_manager:_worker:266 | 工作线程启动: worker-2
2025-08-12 16:43:50 | INFO     | app.core.queue_manager:_worker:266 | 工作线程启动: worker-3
2025-08-12 16:44:08 | INFO     | app.middleware.logging:dispatch:35 | 请求开始 | ID: 24f4b89b-14c9-4b02-9c27-78cdc03a91ca | 方法: POST | URL: http://localhost:8088/api/v1/check-compliance-simple | 客户端IP: 127.0.0.1 | User-Agent: Apifox/1.0.0 (https://apifox.com)
2025-08-12 16:44:08 | INFO     | app.middleware.validation:_validate_request:75 | 跳过中间件验证 | ID: 24f4b89b-14c9-4b02-9c27-78cdc03a91ca | 路径: /api/v1/check-compliance-simple
2025-08-12 16:44:08 | INFO     | app.api.routes:check_compliance_simple:125 | 开始简化合规性检查 | ID: 24f4b89b-14c9-4b02-9c27-78cdc03a91ca | URL: https://test-fds.xinecai.com/group1/M00/02/93/rBIKBmiatqqAXC4lABaSuc9ZQzU547.doc?token=894b64823d6e7b39f52d03ee0cffff92&ts=1754969781
2025-08-12 16:44:08 | INFO     | app.utils.file_info_utils:infer_file_info_from_url:154 | 开始从URL推断文件信息: https://test-fds.xinecai.com/group1/M00/02/93/rBIKBmiatqqAXC4lABaSuc9ZQzU547.doc?token=894b64823d6e7b39f52d03ee0cffff92&ts=1754969781
2025-08-12 16:44:08 | INFO     | app.utils.file_info_utils:infer_file_info_from_url:158 | 推断的文件名: rBIKBmiatqqAXC4lABaSuc9ZQzU547.doc
2025-08-12 16:44:08 | WARNING  | app.utils.file_info_utils:infer_file_extension_and_mime_type:107 | 未识别的文件扩展名: .doc，默认使用docx格式
2025-08-12 16:44:08 | INFO     | app.utils.file_info_utils:infer_file_info_from_url:162 | 推断的扩展名: .docx, MIME类型: application/vnd.openxmlformats-officedocument.wordprocessingml.document
2025-08-12 16:44:10 | INFO     | app.utils.file_info_utils:infer_file_info_from_url:171 | 获取的文件大小: 1479353 字节
2025-08-12 16:44:10 | INFO     | app.utils.file_info_utils:infer_file_info_from_url:182 | 文件信息推断完成: rBIKBmiatqqAXC4lABaSuc9ZQzU547.doc
2025-08-12 16:44:10 | INFO     | app.api.routes:check_compliance_simple:131 | 文件信息推断成功 | ID: 24f4b89b-14c9-4b02-9c27-78cdc03a91ca | 文件: rBIKBmiatqqAXC4lABaSuc9ZQzU547.doc
2025-08-12 16:44:10 | INFO     | app.services.compliance_service:check_compliance:636 | 合规性检查服务开始 | ID: 24f4b89b-14c9-4b02-9c27-78cdc03a91ca
2025-08-12 16:44:10 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 完整合规性检查流水线 | ID: 24f4b89b-14c9-4b02-9c27-78cdc03a91ca | 详情: {}
2025-08-12 16:44:10 | INFO     | app.services.compliance_service:execute_pipeline:376 | 开始执行合规性检查流水线 | ID: 24f4b89b-14c9-4b02-9c27-78cdc03a91ca | 文件: rBIKBmiatqqAXC4lABaSuc9ZQzU547.doc
2025-08-12 16:44:10 | INFO     | app.services.compliance_service:execute_pipeline:386 | 步骤1: 开始验证服务前置条件 | ID: 24f4b89b-14c9-4b02-9c27-78cdc03a91ca
2025-08-12 16:44:10 | INFO     | app.services.compliance_service:validate_prerequisites:73 | 开始验证服务前置条件 | ID: 24f4b89b-14c9-4b02-9c27-78cdc03a91ca
2025-08-12 16:44:10 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 敏感词服务健康检查 | ID: 24f4b89b-14c9-4b02-9c27-78cdc03a91ca | 详情: {}
2025-08-12 16:44:10 | INFO     | app.services.sensitive_word_service:check_health:122 | 检查敏感词服务健康状态: http://************:8087/health
2025-08-12 16:44:10 | INFO     | app.services.sensitive_word_service:check_health:132 | 敏感词服务健康状态: 健康
2025-08-12 16:44:10 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 敏感词服务健康检查 | ID: 24f4b89b-14c9-4b02-9c27-78cdc03a91ca | 状态: 成功 | 耗时: 0.027秒
2025-08-12 16:44:10 | INFO     | app.services.compliance_service:validate_prerequisites:92 | 服务健康检查完成 | ID: 24f4b89b-14c9-4b02-9c27-78cdc03a91ca | 状态: {'file_processor': True, 'ai_model': True, 'sensitive_word': True, 'result_processor': True}
2025-08-12 16:44:10 | INFO     | app.services.compliance_service:execute_pipeline:388 | 步骤1: 服务前置条件验证完成 | ID: 24f4b89b-14c9-4b02-9c27-78cdc03a91ca | 状态: {'file_processor': True, 'ai_model': True, 'sensitive_word': True, 'result_processor': True}
2025-08-12 16:44:10 | INFO     | app.services.compliance_service:execute_pipeline:394 | 步骤2: 开始获取项目信息 | ID: 24f4b89b-14c9-4b02-9c27-78cdc03a91ca
2025-08-12 16:44:10 | INFO     | app.services.compliance_service:execute_pipeline:396 | 步骤2: 项目信息获取完成 | ID: 24f4b89b-14c9-4b02-9c27-78cdc03a91ca
2025-08-12 16:44:10 | INFO     | app.services.compliance_service:execute_pipeline:400 | 步骤3: 开始文件处理阶段 | ID: 24f4b89b-14c9-4b02-9c27-78cdc03a91ca
2025-08-12 16:44:10 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 文件处理阶段 | ID: 24f4b89b-14c9-4b02-9c27-78cdc03a91ca | 详情: {}
2025-08-12 16:44:10 | INFO     | app.services.compliance_service:process_file_stage:114 | 开始文件处理阶段 | ID: 24f4b89b-14c9-4b02-9c27-78cdc03a91ca | 文件: rBIKBmiatqqAXC4lABaSuc9ZQzU547.doc
2025-08-12 16:44:10 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 优化文件处理流程 | ID: 24f4b89b-14c9-4b02-9c27-78cdc03a91ca | 详情: {}
2025-08-12 16:44:10 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 文件下载 | ID: 24f4b89b-14c9-4b02-9c27-78cdc03a91ca | 详情: {}
2025-08-12 16:44:10 | INFO     | app.services.file_processor_v2:download_file:255 | 开始下载文件: https://test-fds.xinecai.com/group1/M00/02/93/rBIKBmiatqqAXC4lABaSuc9ZQzU547.doc?token=894b64823d6e7b39f52d03ee0cffff92&ts=1754969781
2025-08-12 16:44:11 | INFO     | app.services.file_processor_v2:download_file:289 | 文件下载完成: 1479353 字节
2025-08-12 16:44:11 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 文件下载 | ID: 24f4b89b-14c9-4b02-9c27-78cdc03a91ca | 状态: 成功 | 耗时: 1.517秒
2025-08-12 16:44:11 | WARNING  | app.services.file_processor_v2:process_file:515 | MarkItDown不可用，使用备用方案
2025-08-12 16:44:11 | WARNING  | app.services.file_processor_v2:process_file:519 | MarkItDown处理失败: MarkItDown不可用，使用备用方案
2025-08-12 16:44:11 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | DOCX备用提取 | ID: 24f4b89b-14c9-4b02-9c27-78cdc03a91ca | 详情: {}
2025-08-12 16:44:12 | INFO     | app.services.file_processor_v2:extract_docx_content_fallback:401 | DOCX备用提取完成: 36623 字符
2025-08-12 16:44:12 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | DOCX备用提取 | ID: 24f4b89b-14c9-4b02-9c27-78cdc03a91ca | 状态: 成功 | 耗时: 0.290秒
2025-08-12 16:44:12 | INFO     | app.core.logger:log_file_processing:159 | 文件处理 | ID: 24f4b89b-14c9-4b02-9c27-78cdc03a91ca | 文件: rBIKBmiatqqAXC4lABaSuc9ZQzU547.doc | 大小: 1479353 字节 | 处理时间: 0.000秒
2025-08-12 16:44:12 | INFO     | app.services.file_processor_v2:process_file:548 | 文件处理完成(备用方案): rBIKBmiatqqAXC4lABaSuc9ZQzU547.doc, 输出长度: 36683 字符
2025-08-12 16:44:12 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 优化文件处理流程 | ID: 24f4b89b-14c9-4b02-9c27-78cdc03a91ca | 状态: 成功 | 耗时: 1.814秒
2025-08-12 16:44:12 | INFO     | app.services.compliance_service:process_file_stage:131 | 文件处理完成 | ID: 24f4b89b-14c9-4b02-9c27-78cdc03a91ca | 内容长度: 36683 字符
2025-08-12 16:44:12 | INFO     | app.core.logger:log_file_processing:159 | 文件处理 | ID: 24f4b89b-14c9-4b02-9c27-78cdc03a91ca | 文件: rBIKBmiatqqAXC4lABaSuc9ZQzU547.doc | 大小: 1479353 字节 | 处理时间: 0.000秒
2025-08-12 16:44:12 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | file_processing | ID: 24f4b89b-14c9-4b02-9c27-78cdc03a91ca | 状态: 成功 | 耗时: 1.814秒
2025-08-12 16:44:12 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 文件处理阶段 | ID: 24f4b89b-14c9-4b02-9c27-78cdc03a91ca | 状态: 成功 | 耗时: 1.815秒
2025-08-12 16:44:12 | INFO     | app.services.compliance_service:execute_pipeline:405 | 步骤3: 文件处理完成 | ID: 24f4b89b-14c9-4b02-9c27-78cdc03a91ca | 内容长度: 36683
2025-08-12 16:44:12 | INFO     | app.services.compliance_service:execute_pipeline:416 | 步骤4: 开始AI合规性检查阶段 | ID: 24f4b89b-14c9-4b02-9c27-78cdc03a91ca
2025-08-12 16:44:12 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | AI合规性检查阶段 | ID: 24f4b89b-14c9-4b02-9c27-78cdc03a91ca | 详情: {}
2025-08-12 16:44:12 | INFO     | app.services.compliance_service:ai_compliance_check_stage:188 | 开始AI合规性检查 | ID: 24f4b89b-14c9-4b02-9c27-78cdc03a91ca
2025-08-12 16:44:12 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | AI模型调用(尝试1) | ID: 24f4b89b-14c9-4b02-9c27-78cdc03a91ca | 详情: {}
2025-08-12 16:44:12 | INFO     | app.services.ai_model_service:_call_model_with_retry:770 | 调用AI模型: gemini-2.5-flash, 消息数: 2, 尝试: 1/4 | 请求ID: 24f4b89b-14c9-4b02-9c27-78cdc03a91ca
2025-08-12 16:49:17 | INFO     | app.services.ai_model_service:_initialize_client:80 | AI模型客户端初始化成功: gemini-2.5-flash
2025-08-12 16:49:20 | INFO     | app.services.ai_model_service:_initialize_client:80 | AI模型客户端初始化成功: gemini-2.5-flash
2025-08-12 16:49:20 | INFO     | main:lifespan:26 | 招标文件合规性检查助手启动中...
2025-08-12 16:49:20 | INFO     | main:lifespan:27 | 配置信息: 模型=gemini-2.5-flash, 环境=development
2025-08-12 16:49:20 | INFO     | main:lifespan:31 | 启动异步请求队列...
2025-08-12 16:49:20 | INFO     | app.core.queue_manager:start_workers:117 | 启动请求队列工作线程: 4个
2025-08-12 16:49:20 | INFO     | main:lifespan:33 | 请求队列已启动，工作线程数: 4
2025-08-12 16:49:20 | INFO     | main:lifespan:37 | 文档缓存已启用，最大大小: 100MB
2025-08-12 16:49:20 | INFO     | main:lifespan:40 | 内存阈值: 2048MB, 自动GC: True
2025-08-12 16:49:20 | INFO     | app.core.queue_manager:_worker:266 | 工作线程启动: worker-0
2025-08-12 16:49:20 | INFO     | app.core.queue_manager:_worker:266 | 工作线程启动: worker-1
2025-08-12 16:49:20 | INFO     | app.core.queue_manager:_worker:266 | 工作线程启动: worker-2
2025-08-12 16:49:20 | INFO     | app.core.queue_manager:_worker:266 | 工作线程启动: worker-3
2025-08-12 16:49:35 | INFO     | app.middleware.logging:dispatch:35 | 请求开始 | ID: 472292a0-6fdd-4171-93bb-c73e9d98c9f4 | 方法: POST | URL: http://localhost:8088/api/v1/check-compliance-simple | 客户端IP: 127.0.0.1 | User-Agent: Apifox/1.0.0 (https://apifox.com)
2025-08-12 16:49:35 | INFO     | app.middleware.validation:_validate_request:75 | 跳过中间件验证 | ID: 472292a0-6fdd-4171-93bb-c73e9d98c9f4 | 路径: /api/v1/check-compliance-simple
2025-08-12 16:49:35 | INFO     | app.api.routes:check_compliance_simple:125 | 开始简化合规性检查 | ID: 472292a0-6fdd-4171-93bb-c73e9d98c9f4 | URL: https://test-fds.xinecai.com/group1/M00/02/93/rBIKBmiatqqAXC4lABaSuc9ZQzU547.doc?token=894b64823d6e7b39f52d03ee0cffff92&ts=1754969781
2025-08-12 16:49:35 | INFO     | app.utils.file_info_utils:infer_file_info_from_url:154 | 开始从URL推断文件信息: https://test-fds.xinecai.com/group1/M00/02/93/rBIKBmiatqqAXC4lABaSuc9ZQzU547.doc?token=894b64823d6e7b39f52d03ee0cffff92&ts=1754969781
2025-08-12 16:49:35 | INFO     | app.utils.file_info_utils:infer_file_info_from_url:158 | 推断的文件名: rBIKBmiatqqAXC4lABaSuc9ZQzU547.doc
2025-08-12 16:49:35 | WARNING  | app.utils.file_info_utils:infer_file_extension_and_mime_type:107 | 未识别的文件扩展名: .doc，默认使用docx格式
2025-08-12 16:49:35 | INFO     | app.utils.file_info_utils:infer_file_info_from_url:162 | 推断的扩展名: .docx, MIME类型: application/vnd.openxmlformats-officedocument.wordprocessingml.document
2025-08-12 16:49:36 | INFO     | app.utils.file_info_utils:infer_file_info_from_url:171 | 获取的文件大小: 1479353 字节
2025-08-12 16:49:36 | INFO     | app.utils.file_info_utils:infer_file_info_from_url:182 | 文件信息推断完成: rBIKBmiatqqAXC4lABaSuc9ZQzU547.doc
2025-08-12 16:49:36 | INFO     | app.api.routes:check_compliance_simple:131 | 文件信息推断成功 | ID: 472292a0-6fdd-4171-93bb-c73e9d98c9f4 | 文件: rBIKBmiatqqAXC4lABaSuc9ZQzU547.doc
2025-08-12 16:49:36 | INFO     | app.services.compliance_service:check_compliance:636 | 合规性检查服务开始 | ID: 472292a0-6fdd-4171-93bb-c73e9d98c9f4
2025-08-12 16:49:36 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 完整合规性检查流水线 | ID: 472292a0-6fdd-4171-93bb-c73e9d98c9f4 | 详情: {}
2025-08-12 16:49:36 | INFO     | app.services.compliance_service:execute_pipeline:376 | 开始执行合规性检查流水线 | ID: 472292a0-6fdd-4171-93bb-c73e9d98c9f4 | 文件: rBIKBmiatqqAXC4lABaSuc9ZQzU547.doc
2025-08-12 16:49:36 | INFO     | app.services.compliance_service:execute_pipeline:386 | 步骤1: 开始验证服务前置条件 | ID: 472292a0-6fdd-4171-93bb-c73e9d98c9f4
2025-08-12 16:49:36 | INFO     | app.services.compliance_service:validate_prerequisites:73 | 开始验证服务前置条件 | ID: 472292a0-6fdd-4171-93bb-c73e9d98c9f4
2025-08-12 16:49:36 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 敏感词服务健康检查 | ID: 472292a0-6fdd-4171-93bb-c73e9d98c9f4 | 详情: {}
2025-08-12 16:49:36 | INFO     | app.services.sensitive_word_service:check_health:122 | 检查敏感词服务健康状态: http://************:8087/health
2025-08-12 16:49:36 | INFO     | app.services.sensitive_word_service:check_health:132 | 敏感词服务健康状态: 健康
2025-08-12 16:49:36 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 敏感词服务健康检查 | ID: 472292a0-6fdd-4171-93bb-c73e9d98c9f4 | 状态: 成功 | 耗时: 0.011秒
2025-08-12 16:49:36 | INFO     | app.services.compliance_service:validate_prerequisites:92 | 服务健康检查完成 | ID: 472292a0-6fdd-4171-93bb-c73e9d98c9f4 | 状态: {'file_processor': True, 'ai_model': True, 'sensitive_word': True, 'result_processor': True}
2025-08-12 16:49:36 | INFO     | app.services.compliance_service:execute_pipeline:388 | 步骤1: 服务前置条件验证完成 | ID: 472292a0-6fdd-4171-93bb-c73e9d98c9f4 | 状态: {'file_processor': True, 'ai_model': True, 'sensitive_word': True, 'result_processor': True}
2025-08-12 16:49:36 | INFO     | app.services.compliance_service:execute_pipeline:394 | 步骤2: 开始获取项目信息 | ID: 472292a0-6fdd-4171-93bb-c73e9d98c9f4
2025-08-12 16:49:36 | INFO     | app.services.compliance_service:execute_pipeline:396 | 步骤2: 项目信息获取完成 | ID: 472292a0-6fdd-4171-93bb-c73e9d98c9f4
2025-08-12 16:49:36 | INFO     | app.services.compliance_service:execute_pipeline:400 | 步骤3: 开始文件处理阶段 | ID: 472292a0-6fdd-4171-93bb-c73e9d98c9f4
2025-08-12 16:49:36 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 文件处理阶段 | ID: 472292a0-6fdd-4171-93bb-c73e9d98c9f4 | 详情: {}
2025-08-12 16:49:36 | INFO     | app.services.compliance_service:process_file_stage:114 | 开始文件处理阶段 | ID: 472292a0-6fdd-4171-93bb-c73e9d98c9f4 | 文件: rBIKBmiatqqAXC4lABaSuc9ZQzU547.doc
2025-08-12 16:49:36 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 优化文件处理流程 | ID: 472292a0-6fdd-4171-93bb-c73e9d98c9f4 | 详情: {}
2025-08-12 16:49:36 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 文件下载 | ID: 472292a0-6fdd-4171-93bb-c73e9d98c9f4 | 详情: {}
2025-08-12 16:49:36 | INFO     | app.services.file_processor_v2:download_file:255 | 开始下载文件: https://test-fds.xinecai.com/group1/M00/02/93/rBIKBmiatqqAXC4lABaSuc9ZQzU547.doc?token=894b64823d6e7b39f52d03ee0cffff92&ts=1754969781
2025-08-12 16:49:38 | INFO     | app.services.file_processor_v2:download_file:289 | 文件下载完成: 1479353 字节
2025-08-12 16:49:38 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 文件下载 | ID: 472292a0-6fdd-4171-93bb-c73e9d98c9f4 | 状态: 成功 | 耗时: 1.166秒
2025-08-12 16:49:38 | WARNING  | app.services.file_processor_v2:process_file:515 | MarkItDown不可用，使用备用方案
2025-08-12 16:49:38 | WARNING  | app.services.file_processor_v2:process_file:519 | MarkItDown处理失败: MarkItDown不可用，使用备用方案
2025-08-12 16:49:38 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | DOCX备用提取 | ID: 472292a0-6fdd-4171-93bb-c73e9d98c9f4 | 详情: {}
2025-08-12 16:49:38 | INFO     | app.services.file_processor_v2:extract_docx_content_fallback:401 | DOCX备用提取完成: 36623 字符
2025-08-12 16:49:38 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | DOCX备用提取 | ID: 472292a0-6fdd-4171-93bb-c73e9d98c9f4 | 状态: 成功 | 耗时: 0.265秒
2025-08-12 16:49:38 | INFO     | app.core.logger:log_file_processing:159 | 文件处理 | ID: 472292a0-6fdd-4171-93bb-c73e9d98c9f4 | 文件: rBIKBmiatqqAXC4lABaSuc9ZQzU547.doc | 大小: 1479353 字节 | 处理时间: 0.000秒
2025-08-12 16:49:38 | INFO     | app.services.file_processor_v2:process_file:548 | 文件处理完成(备用方案): rBIKBmiatqqAXC4lABaSuc9ZQzU547.doc, 输出长度: 36683 字符
2025-08-12 16:49:38 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 优化文件处理流程 | ID: 472292a0-6fdd-4171-93bb-c73e9d98c9f4 | 状态: 成功 | 耗时: 1.440秒
2025-08-12 16:49:38 | INFO     | app.services.compliance_service:process_file_stage:131 | 文件处理完成 | ID: 472292a0-6fdd-4171-93bb-c73e9d98c9f4 | 内容长度: 36683 字符
2025-08-12 16:49:38 | INFO     | app.core.logger:log_file_processing:159 | 文件处理 | ID: 472292a0-6fdd-4171-93bb-c73e9d98c9f4 | 文件: rBIKBmiatqqAXC4lABaSuc9ZQzU547.doc | 大小: 1479353 字节 | 处理时间: 0.000秒
2025-08-12 16:49:38 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | file_processing | ID: 472292a0-6fdd-4171-93bb-c73e9d98c9f4 | 状态: 成功 | 耗时: 1.440秒
2025-08-12 16:49:38 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 文件处理阶段 | ID: 472292a0-6fdd-4171-93bb-c73e9d98c9f4 | 状态: 成功 | 耗时: 1.442秒
2025-08-12 16:49:38 | INFO     | app.services.compliance_service:execute_pipeline:405 | 步骤3: 文件处理完成 | ID: 472292a0-6fdd-4171-93bb-c73e9d98c9f4 | 内容长度: 36683
2025-08-12 16:49:38 | INFO     | app.services.compliance_service:execute_pipeline:416 | 步骤4: 开始AI合规性检查阶段 | ID: 472292a0-6fdd-4171-93bb-c73e9d98c9f4
2025-08-12 16:49:38 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | AI合规性检查阶段 | ID: 472292a0-6fdd-4171-93bb-c73e9d98c9f4 | 详情: {}
2025-08-12 16:49:38 | INFO     | app.services.compliance_service:ai_compliance_check_stage:188 | 开始AI合规性检查 | ID: 472292a0-6fdd-4171-93bb-c73e9d98c9f4
2025-08-12 16:49:38 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | AI模型调用(尝试1) | ID: 472292a0-6fdd-4171-93bb-c73e9d98c9f4 | 详情: {}
2025-08-12 16:49:38 | INFO     | app.services.ai_model_service:_call_model_with_retry:770 | 调用AI模型: gemini-2.5-flash, 消息数: 2, 尝试: 1/4 | 请求ID: 472292a0-6fdd-4171-93bb-c73e9d98c9f4
2025-08-12 16:55:48 | INFO     | app.services.ai_model_service:_initialize_client:80 | AI模型客户端初始化成功: gemini-2.5-flash
2025-08-12 16:55:50 | INFO     | app.services.ai_model_service:_initialize_client:80 | AI模型客户端初始化成功: gemini-2.5-flash
2025-08-12 16:55:50 | INFO     | main:lifespan:26 | 招标文件合规性检查助手启动中...
2025-08-12 16:55:50 | INFO     | main:lifespan:27 | 配置信息: 模型=gemini-2.5-flash, 环境=development
2025-08-12 16:55:50 | INFO     | main:lifespan:31 | 启动异步请求队列...
2025-08-12 16:55:50 | INFO     | app.core.queue_manager:start_workers:117 | 启动请求队列工作线程: 4个
2025-08-12 16:55:50 | INFO     | main:lifespan:33 | 请求队列已启动，工作线程数: 4
2025-08-12 16:55:50 | INFO     | main:lifespan:37 | 文档缓存已启用，最大大小: 100MB
2025-08-12 16:55:50 | INFO     | main:lifespan:40 | 内存阈值: 2048MB, 自动GC: True
2025-08-12 16:55:50 | INFO     | app.core.queue_manager:_worker:266 | 工作线程启动: worker-0
2025-08-12 16:55:50 | INFO     | app.core.queue_manager:_worker:266 | 工作线程启动: worker-1
2025-08-12 16:55:50 | INFO     | app.core.queue_manager:_worker:266 | 工作线程启动: worker-2
2025-08-12 16:55:50 | INFO     | app.core.queue_manager:_worker:266 | 工作线程启动: worker-3
2025-08-12 16:55:57 | INFO     | app.middleware.logging:dispatch:35 | 请求开始 | ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257 | 方法: POST | URL: http://localhost:8088/api/v1/check-compliance-simple | 客户端IP: 127.0.0.1 | User-Agent: Apifox/1.0.0 (https://apifox.com)
2025-08-12 16:55:57 | INFO     | app.middleware.validation:_validate_request:75 | 跳过中间件验证 | ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257 | 路径: /api/v1/check-compliance-simple
2025-08-12 16:55:57 | INFO     | app.api.routes:check_compliance_simple:125 | 开始简化合规性检查 | ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257 | URL: https://test-fds.xinecai.com/group1/M00/02/93/rBIKBmiatqqAXC4lABaSuc9ZQzU547.doc?token=894b64823d6e7b39f52d03ee0cffff92&ts=1754969781
2025-08-12 16:55:57 | INFO     | app.utils.file_info_utils:infer_file_info_from_url:154 | 开始从URL推断文件信息: https://test-fds.xinecai.com/group1/M00/02/93/rBIKBmiatqqAXC4lABaSuc9ZQzU547.doc?token=894b64823d6e7b39f52d03ee0cffff92&ts=1754969781
2025-08-12 16:55:57 | INFO     | app.utils.file_info_utils:infer_file_info_from_url:158 | 推断的文件名: rBIKBmiatqqAXC4lABaSuc9ZQzU547.doc
2025-08-12 16:55:57 | WARNING  | app.utils.file_info_utils:infer_file_extension_and_mime_type:107 | 未识别的文件扩展名: .doc，默认使用docx格式
2025-08-12 16:55:57 | INFO     | app.utils.file_info_utils:infer_file_info_from_url:162 | 推断的扩展名: .docx, MIME类型: application/vnd.openxmlformats-officedocument.wordprocessingml.document
2025-08-12 16:55:58 | INFO     | app.utils.file_info_utils:infer_file_info_from_url:171 | 获取的文件大小: 1479353 字节
2025-08-12 16:55:58 | INFO     | app.utils.file_info_utils:infer_file_info_from_url:182 | 文件信息推断完成: rBIKBmiatqqAXC4lABaSuc9ZQzU547.doc
2025-08-12 16:55:58 | INFO     | app.api.routes:check_compliance_simple:131 | 文件信息推断成功 | ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257 | 文件: rBIKBmiatqqAXC4lABaSuc9ZQzU547.doc
2025-08-12 16:55:58 | INFO     | app.services.compliance_service:check_compliance:636 | 合规性检查服务开始 | ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257
2025-08-12 16:55:58 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 完整合规性检查流水线 | ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257 | 详情: {}
2025-08-12 16:55:58 | INFO     | app.services.compliance_service:execute_pipeline:376 | 开始执行合规性检查流水线 | ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257 | 文件: rBIKBmiatqqAXC4lABaSuc9ZQzU547.doc
2025-08-12 16:55:58 | INFO     | app.services.compliance_service:execute_pipeline:386 | 步骤1: 开始验证服务前置条件 | ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257
2025-08-12 16:55:58 | INFO     | app.services.compliance_service:validate_prerequisites:73 | 开始验证服务前置条件 | ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257
2025-08-12 16:55:58 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 敏感词服务健康检查 | ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257 | 详情: {}
2025-08-12 16:55:58 | INFO     | app.services.sensitive_word_service:check_health:122 | 检查敏感词服务健康状态: http://************:8087/health
2025-08-12 16:55:58 | INFO     | app.services.sensitive_word_service:check_health:132 | 敏感词服务健康状态: 健康
2025-08-12 16:55:58 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 敏感词服务健康检查 | ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257 | 状态: 成功 | 耗时: 0.028秒
2025-08-12 16:55:58 | INFO     | app.services.compliance_service:validate_prerequisites:92 | 服务健康检查完成 | ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257 | 状态: {'file_processor': True, 'ai_model': True, 'sensitive_word': True, 'result_processor': True}
2025-08-12 16:55:58 | INFO     | app.services.compliance_service:execute_pipeline:388 | 步骤1: 服务前置条件验证完成 | ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257 | 状态: {'file_processor': True, 'ai_model': True, 'sensitive_word': True, 'result_processor': True}
2025-08-12 16:55:58 | INFO     | app.services.compliance_service:execute_pipeline:394 | 步骤2: 开始获取项目信息 | ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257
2025-08-12 16:55:58 | INFO     | app.services.compliance_service:execute_pipeline:396 | 步骤2: 项目信息获取完成 | ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257
2025-08-12 16:55:58 | INFO     | app.services.compliance_service:execute_pipeline:400 | 步骤3: 开始文件处理阶段 | ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257
2025-08-12 16:55:58 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 文件处理阶段 | ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257 | 详情: {}
2025-08-12 16:55:58 | INFO     | app.services.compliance_service:process_file_stage:114 | 开始文件处理阶段 | ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257 | 文件: rBIKBmiatqqAXC4lABaSuc9ZQzU547.doc
2025-08-12 16:55:58 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 优化文件处理流程 | ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257 | 详情: {}
2025-08-12 16:55:58 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 文件下载 | ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257 | 详情: {}
2025-08-12 16:55:58 | INFO     | app.services.file_processor_v2:download_file:255 | 开始下载文件: https://test-fds.xinecai.com/group1/M00/02/93/rBIKBmiatqqAXC4lABaSuc9ZQzU547.doc?token=894b64823d6e7b39f52d03ee0cffff92&ts=1754969781
2025-08-12 16:55:59 | INFO     | app.services.file_processor_v2:download_file:289 | 文件下载完成: 1479353 字节
2025-08-12 16:55:59 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 文件下载 | ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257 | 状态: 成功 | 耗时: 1.121秒
2025-08-12 16:55:59 | WARNING  | app.services.file_processor_v2:process_file:515 | MarkItDown不可用，使用备用方案
2025-08-12 16:55:59 | WARNING  | app.services.file_processor_v2:process_file:519 | MarkItDown处理失败: MarkItDown不可用，使用备用方案
2025-08-12 16:55:59 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | DOCX备用提取 | ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257 | 详情: {}
2025-08-12 16:56:00 | INFO     | app.services.file_processor_v2:extract_docx_content_fallback:401 | DOCX备用提取完成: 36623 字符
2025-08-12 16:56:00 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | DOCX备用提取 | ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257 | 状态: 成功 | 耗时: 0.345秒
2025-08-12 16:56:00 | INFO     | app.core.logger:log_file_processing:159 | 文件处理 | ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257 | 文件: rBIKBmiatqqAXC4lABaSuc9ZQzU547.doc | 大小: 1479353 字节 | 处理时间: 0.000秒
2025-08-12 16:56:00 | INFO     | app.services.file_processor_v2:process_file:548 | 文件处理完成(备用方案): rBIKBmiatqqAXC4lABaSuc9ZQzU547.doc, 输出长度: 36683 字符
2025-08-12 16:56:00 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 优化文件处理流程 | ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257 | 状态: 成功 | 耗时: 1.466秒
2025-08-12 16:56:00 | INFO     | app.services.compliance_service:process_file_stage:131 | 文件处理完成 | ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257 | 内容长度: 36683 字符
2025-08-12 16:56:00 | INFO     | app.core.logger:log_file_processing:159 | 文件处理 | ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257 | 文件: rBIKBmiatqqAXC4lABaSuc9ZQzU547.doc | 大小: 1479353 字节 | 处理时间: 0.000秒
2025-08-12 16:56:00 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | file_processing | ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257 | 状态: 成功 | 耗时: 1.467秒
2025-08-12 16:56:00 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 文件处理阶段 | ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257 | 状态: 成功 | 耗时: 1.467秒
2025-08-12 16:56:00 | INFO     | app.services.compliance_service:execute_pipeline:405 | 步骤3: 文件处理完成 | ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257 | 内容长度: 36683
2025-08-12 16:56:00 | INFO     | app.services.compliance_service:execute_pipeline:416 | 步骤4: 开始AI合规性检查阶段 | ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257
2025-08-12 16:56:00 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | AI合规性检查阶段 | ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257 | 详情: {}
2025-08-12 16:56:00 | INFO     | app.services.compliance_service:ai_compliance_check_stage:188 | 开始AI合规性检查 | ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257
2025-08-12 16:56:00 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | AI模型调用(尝试1) | ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257 | 详情: {}
2025-08-12 16:56:00 | INFO     | app.services.ai_model_service:_call_model_with_retry:770 | 调用AI模型: gemini-2.5-flash, 消息数: 2, 尝试: 1/4 | 请求ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257
2025-08-12 16:56:15 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | AI模型调用(尝试1) | ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257 | 状态: 失败 | 耗时: 15.884秒
2025-08-12 16:56:15 | ERROR    | app.core.logger:__exit__:421 | 操作 AI模型调用(尝试1) 发生异常: Request timed out.
2025-08-12 16:56:15 | INFO     | app.core.logger:log_api_call:170 | API调用 | OpenAI-gemini-2.5-flash | ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257 | 状态: 失败 | 耗时: 0.000秒
2025-08-12 16:56:15 | ERROR    | app.core.error_logger:log_service_error:59 | 服务错误 | AIModelService.call_model_with_retry | ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257 | 错误: AIModelError: AI模型调用异常: Request timed out. (模型: gemini-2.5-flash, 原因: Request timed out.)
2025-08-12 16:56:15 | ERROR    | app.core.logger:wrapper:452 | 函数调用失败: call_model | 耗时: 15.884秒 | 错误: AI模型调用异常: Request timed out. (模型: gemini-2.5-flash, 原因: Request timed out.)
2025-08-12 16:56:15 | ERROR    | app.services.ai_model_service:check_compliance:1212 | AI模型错误 | 请求ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257 | 错误: AI模型调用异常: Request timed out. (模型: gemini-2.5-flash, 原因: Request timed out.)
2025-08-12 16:56:15 | ERROR    | app.core.logger:wrapper:452 | 函数调用失败: check_compliance | 耗时: 15.888秒 | 错误: AI模型调用异常: Request timed out. (模型: gemini-2.5-flash, 原因: Request timed out.)
2025-08-12 16:56:15 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | AI合规性检查阶段 | ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257 | 状态: 失败 | 耗时: 15.892秒
2025-08-12 16:56:15 | ERROR    | app.core.logger:__exit__:421 | 操作 AI合规性检查阶段 发生异常: AI模型调用异常: Request timed out. (模型: gemini-2.5-flash, 原因: Request timed out.)
2025-08-12 16:56:15 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | ai_model_call | ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257 | 状态: 失败 | 耗时: 15.894秒
2025-08-12 16:56:15 | ERROR    | app.core.logger:wrapper:452 | 函数调用失败: ai_compliance_check_stage | 耗时: 15.894秒 | 错误: AI合规性检查阶段失败: AI模型调用异常: Request timed out. (模型: gemini-2.5-flash, 原因: Request timed out.)
2025-08-12 16:56:15 | WARNING  | app.services.compliance_service:execute_pipeline:427 | 步骤4: AI合规性检查异常 | ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257 | 错误: AI合规性检查阶段失败: AI模型调用异常: Request timed out. (模型: gemini-2.5-flash, 原因: Request timed out.)
2025-08-12 16:56:15 | WARNING  | app.services.compliance_service:execute_pipeline:431 | AI检查失败，使用降级策略 | ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257
2025-08-12 16:56:15 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 敏感词检测阶段 | ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257 | 详情: {}
2025-08-12 16:56:15 | INFO     | app.services.compliance_service:sensitive_word_check_stage:245 | 开始敏感词检测 | ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257
2025-08-12 16:56:15 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 敏感词服务健康检查 | ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257 | 详情: {}
2025-08-12 16:56:15 | INFO     | app.services.sensitive_word_service:check_health:122 | 检查敏感词服务健康状态: http://************:8087/health
2025-08-12 16:56:16 | INFO     | app.services.sensitive_word_service:check_health:132 | 敏感词服务健康状态: 健康
2025-08-12 16:56:16 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 敏感词服务健康检查 | ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257 | 状态: 成功 | 耗时: 0.259秒
2025-08-12 16:56:16 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 敏感词检测 | ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257 | 详情: {}
2025-08-12 16:56:16 | INFO     | app.services.sensitive_word_service:detect_sensitive_words:214 | 开始敏感词检测: 内容长度=36683, 政府采购=False
2025-08-12 16:56:16 | INFO     | app.services.sensitive_word_service:detect_sensitive_words:223 | 敏感词检测超时设置: 60.0秒 | ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257
2025-08-12 16:56:16 | INFO     | app.services.sensitive_word_service:_convert_to_target_format:376 | 敏感词转换完成: 原始3个，有效3个
2025-08-12 16:56:16 | INFO     | app.core.logger:log_api_call:170 | API调用 | 敏感词检测API | ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257 | 状态: 成功 | 耗时: 0.000秒
2025-08-12 16:56:16 | INFO     | app.services.sensitive_word_service:detect_sensitive_words:269 | 敏感词检测完成: 发现 3 个敏感词
2025-08-12 16:56:16 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 敏感词检测 | ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257 | 状态: 成功 | 耗时: 0.591秒
2025-08-12 16:56:16 | INFO     | app.services.compliance_service:sensitive_word_check_stage:252 | 敏感词检测完成 | ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257 | 发现敏感词: 3个
2025-08-12 16:56:16 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | sensitive_word | ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257 | 状态: 成功 | 耗时: 0.850秒
2025-08-12 16:56:16 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 敏感词检测阶段 | ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257 | 状态: 成功 | 耗时: 0.850秒
2025-08-12 16:56:16 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 结果聚合阶段 | ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257 | 详情: {}
2025-08-12 16:56:16 | INFO     | app.services.compliance_service:result_aggregation_stage:305 | 开始结果聚合 | ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257
2025-08-12 16:56:16 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 结果聚合 | ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257 | 详情: {}
2025-08-12 16:56:16 | INFO     | app.services.result_processor:aggregate_results:334 | 开始结果聚合: 敏感词3个，检查结果0个 | ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257
2025-08-12 16:56:16 | INFO     | app.services.result_processor:validate_sensitive_words:109 | 敏感词验证完成: 原始3个，有效3个
2025-08-12 16:56:16 | INFO     | app.services.result_processor:deduplicate_sensitive_words:245 | 敏感词去重完成: 原始3个，去重后3个
2025-08-12 16:56:16 | INFO     | app.services.result_processor:_verify_deduplication_integrity:954 | 去重完整性验证 | ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257 | 状态: 完美 | 输入: 3个 | 输出: 3个 | 出现次数变化: 50 -> 50
2025-08-12 16:56:16 | INFO     | app.services.result_processor:validate_check_results:199 | 检查结果验证完成: 原始0个，有效0个
2025-08-12 16:56:16 | INFO     | app.services.result_processor:prioritize_check_results:283 | 检查结果排序完成: 0个结果
2025-08-12 16:56:16 | INFO     | app.services.result_processor:_verify_final_integrity:754 | 完整性报告 | ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257 | 敏感词保留率: 100.0% | 检查结果保留率: 100.0%
2025-08-12 16:56:16 | INFO     | app.services.result_processor:aggregate_results:396 | 结果聚合完成: 敏感词3个，检查结果0个 | ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257
2025-08-12 16:56:16 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 结果聚合 | ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257 | 状态: 成功 | 耗时: 0.005秒
2025-08-12 16:56:16 | INFO     | app.services.result_processor:validate_final_response:490 | 响应验证完成: 敏感词3个，检查结果0个
2025-08-12 16:56:16 | INFO     | app.services.compliance_service:result_aggregation_stage:312 | 结果聚合完成 | ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257 | 敏感词: 3个 | 检查结果: 0个
2025-08-12 16:56:16 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | result_aggregation | ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257 | 状态: 成功 | 耗时: 0.008秒
2025-08-12 16:56:16 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 结果聚合阶段 | ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257 | 状态: 成功 | 耗时: 0.008秒
2025-08-12 16:56:16 | INFO     | app.services.compliance_service:_add_pipeline_metadata:538 | 流水线元数据 | ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257 | 处理时间: 18.252秒 | 降级使用: True | 敏感词数量: 3 | 检查结果数量: 0
2025-08-12 16:56:16 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | total_pipeline | ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257 | 状态: 成功 | 耗时: 18.252秒
2025-08-12 16:56:16 | INFO     | app.services.compliance_service:execute_pipeline:479 | 合规性检查流水线完成 | ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257 | 耗时: 18.252秒 | 降级: True
2025-08-12 16:56:16 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 完整合规性检查流水线 | ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257 | 状态: 成功 | 耗时: 18.254秒
2025-08-12 16:56:16 | INFO     | app.services.compliance_service:check_compliance:641 | 合规性检查服务完成 | ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257
2025-08-12 16:56:16 | INFO     | app.api.routes:check_compliance_simple:156 | 简化合规性检查完成 | ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257 | 文件: rBIKBmiatqqAXC4lABaSuc9ZQzU547.doc | 敏感词: 3个 | 检查结果: 0个
2025-08-12 16:56:16 | INFO     | app.middleware.logging:dispatch:48 | 请求完成 | ID: 3b02ab6d-5743-45fb-bf3f-e97e8f07a257 | 状态码: 200 | 耗时: 19.284秒
2025-08-12 16:58:18 | INFO     | app.services.ai_model_service:_initialize_client:80 | AI模型客户端初始化成功: gemini-2.5-flash
2025-08-12 16:58:21 | INFO     | app.services.ai_model_service:_initialize_client:80 | AI模型客户端初始化成功: gemini-2.5-flash
2025-08-12 16:58:21 | INFO     | main:lifespan:26 | 招标文件合规性检查助手启动中...
2025-08-12 16:58:21 | INFO     | main:lifespan:27 | 配置信息: 模型=gemini-2.5-flash, 环境=development
2025-08-12 16:58:21 | INFO     | main:lifespan:31 | 启动异步请求队列...
2025-08-12 16:58:21 | INFO     | app.core.queue_manager:start_workers:117 | 启动请求队列工作线程: 4个
2025-08-12 16:58:21 | INFO     | main:lifespan:33 | 请求队列已启动，工作线程数: 4
2025-08-12 16:58:21 | INFO     | main:lifespan:37 | 文档缓存已启用，最大大小: 100MB
2025-08-12 16:58:21 | INFO     | main:lifespan:40 | 内存阈值: 2048MB, 自动GC: True
2025-08-12 16:58:21 | INFO     | app.core.queue_manager:_worker:266 | 工作线程启动: worker-0
2025-08-12 16:58:21 | INFO     | app.core.queue_manager:_worker:266 | 工作线程启动: worker-1
2025-08-12 16:58:21 | INFO     | app.core.queue_manager:_worker:266 | 工作线程启动: worker-2
2025-08-12 16:58:21 | INFO     | app.core.queue_manager:_worker:266 | 工作线程启动: worker-3
2025-08-12 16:58:35 | INFO     | app.middleware.logging:dispatch:35 | 请求开始 | ID: 4a256eed-6404-4e6d-8a67-549edb5c423f | 方法: POST | URL: http://localhost:8088/api/v1/check-compliance-simple | 客户端IP: 127.0.0.1 | User-Agent: Apifox/1.0.0 (https://apifox.com)
2025-08-12 16:58:35 | INFO     | app.middleware.validation:_validate_request:75 | 跳过中间件验证 | ID: 4a256eed-6404-4e6d-8a67-549edb5c423f | 路径: /api/v1/check-compliance-simple
2025-08-12 16:58:35 | INFO     | app.api.routes:check_compliance_simple:125 | 开始简化合规性检查 | ID: 4a256eed-6404-4e6d-8a67-549edb5c423f | URL: https://test-fds.xinecai.com/group1/M00/02/93/rBIKBmiatqqAXC4lABaSuc9ZQzU547.doc?token=894b64823d6e7b39f52d03ee0cffff92&ts=1754969781
2025-08-12 16:58:35 | INFO     | app.utils.file_info_utils:infer_file_info_from_url:154 | 开始从URL推断文件信息: https://test-fds.xinecai.com/group1/M00/02/93/rBIKBmiatqqAXC4lABaSuc9ZQzU547.doc?token=894b64823d6e7b39f52d03ee0cffff92&ts=1754969781
2025-08-12 16:58:35 | INFO     | app.utils.file_info_utils:infer_file_info_from_url:158 | 推断的文件名: rBIKBmiatqqAXC4lABaSuc9ZQzU547.doc
2025-08-12 16:58:35 | WARNING  | app.utils.file_info_utils:infer_file_extension_and_mime_type:107 | 未识别的文件扩展名: .doc，默认使用docx格式
2025-08-12 16:58:35 | INFO     | app.utils.file_info_utils:infer_file_info_from_url:162 | 推断的扩展名: .docx, MIME类型: application/vnd.openxmlformats-officedocument.wordprocessingml.document
2025-08-12 16:58:36 | INFO     | app.utils.file_info_utils:infer_file_info_from_url:171 | 获取的文件大小: 1479353 字节
2025-08-12 16:58:36 | INFO     | app.utils.file_info_utils:infer_file_info_from_url:182 | 文件信息推断完成: rBIKBmiatqqAXC4lABaSuc9ZQzU547.doc
2025-08-12 16:58:36 | INFO     | app.api.routes:check_compliance_simple:131 | 文件信息推断成功 | ID: 4a256eed-6404-4e6d-8a67-549edb5c423f | 文件: rBIKBmiatqqAXC4lABaSuc9ZQzU547.doc
2025-08-12 16:58:36 | INFO     | app.services.compliance_service:check_compliance:636 | 合规性检查服务开始 | ID: 4a256eed-6404-4e6d-8a67-549edb5c423f
2025-08-12 16:58:36 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 完整合规性检查流水线 | ID: 4a256eed-6404-4e6d-8a67-549edb5c423f | 详情: {}
2025-08-12 16:58:36 | INFO     | app.services.compliance_service:execute_pipeline:376 | 开始执行合规性检查流水线 | ID: 4a256eed-6404-4e6d-8a67-549edb5c423f | 文件: rBIKBmiatqqAXC4lABaSuc9ZQzU547.doc
2025-08-12 16:58:36 | INFO     | app.services.compliance_service:execute_pipeline:386 | 步骤1: 开始验证服务前置条件 | ID: 4a256eed-6404-4e6d-8a67-549edb5c423f
2025-08-12 16:58:36 | INFO     | app.services.compliance_service:validate_prerequisites:73 | 开始验证服务前置条件 | ID: 4a256eed-6404-4e6d-8a67-549edb5c423f
2025-08-12 16:58:36 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 敏感词服务健康检查 | ID: 4a256eed-6404-4e6d-8a67-549edb5c423f | 详情: {}
2025-08-12 16:58:36 | INFO     | app.services.sensitive_word_service:check_health:122 | 检查敏感词服务健康状态: http://************:8087/health
2025-08-12 16:58:36 | INFO     | app.services.sensitive_word_service:check_health:132 | 敏感词服务健康状态: 健康
2025-08-12 16:58:36 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 敏感词服务健康检查 | ID: 4a256eed-6404-4e6d-8a67-549edb5c423f | 状态: 成功 | 耗时: 0.168秒
2025-08-12 16:58:36 | INFO     | app.services.compliance_service:validate_prerequisites:92 | 服务健康检查完成 | ID: 4a256eed-6404-4e6d-8a67-549edb5c423f | 状态: {'file_processor': True, 'ai_model': True, 'sensitive_word': True, 'result_processor': True}
2025-08-12 16:58:36 | INFO     | app.services.compliance_service:execute_pipeline:388 | 步骤1: 服务前置条件验证完成 | ID: 4a256eed-6404-4e6d-8a67-549edb5c423f | 状态: {'file_processor': True, 'ai_model': True, 'sensitive_word': True, 'result_processor': True}
2025-08-12 16:58:36 | INFO     | app.services.compliance_service:execute_pipeline:394 | 步骤2: 开始获取项目信息 | ID: 4a256eed-6404-4e6d-8a67-549edb5c423f
2025-08-12 16:58:36 | INFO     | app.services.compliance_service:execute_pipeline:396 | 步骤2: 项目信息获取完成 | ID: 4a256eed-6404-4e6d-8a67-549edb5c423f
2025-08-12 16:58:36 | INFO     | app.services.compliance_service:execute_pipeline:400 | 步骤3: 开始文件处理阶段 | ID: 4a256eed-6404-4e6d-8a67-549edb5c423f
2025-08-12 16:58:36 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 文件处理阶段 | ID: 4a256eed-6404-4e6d-8a67-549edb5c423f | 详情: {}
2025-08-12 16:58:36 | INFO     | app.services.compliance_service:process_file_stage:114 | 开始文件处理阶段 | ID: 4a256eed-6404-4e6d-8a67-549edb5c423f | 文件: rBIKBmiatqqAXC4lABaSuc9ZQzU547.doc
2025-08-12 16:58:36 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 优化文件处理流程 | ID: 4a256eed-6404-4e6d-8a67-549edb5c423f | 详情: {}
2025-08-12 16:58:36 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 文件下载 | ID: 4a256eed-6404-4e6d-8a67-549edb5c423f | 详情: {}
2025-08-12 16:58:36 | INFO     | app.services.file_processor_v2:download_file:255 | 开始下载文件: https://test-fds.xinecai.com/group1/M00/02/93/rBIKBmiatqqAXC4lABaSuc9ZQzU547.doc?token=894b64823d6e7b39f52d03ee0cffff92&ts=1754969781
2025-08-12 16:58:39 | INFO     | app.services.file_processor_v2:download_file:289 | 文件下载完成: 1479353 字节
2025-08-12 16:58:39 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 文件下载 | ID: 4a256eed-6404-4e6d-8a67-549edb5c423f | 状态: 成功 | 耗时: 3.138秒
2025-08-12 16:58:39 | WARNING  | app.services.file_processor_v2:process_file:515 | MarkItDown不可用，使用备用方案
2025-08-12 16:58:39 | WARNING  | app.services.file_processor_v2:process_file:519 | MarkItDown处理失败: MarkItDown不可用，使用备用方案
2025-08-12 16:58:39 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | DOCX备用提取 | ID: 4a256eed-6404-4e6d-8a67-549edb5c423f | 详情: {}
2025-08-12 16:58:39 | INFO     | app.services.file_processor_v2:extract_docx_content_fallback:401 | DOCX备用提取完成: 36623 字符
2025-08-12 16:58:39 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | DOCX备用提取 | ID: 4a256eed-6404-4e6d-8a67-549edb5c423f | 状态: 成功 | 耗时: 0.350秒
2025-08-12 16:58:39 | INFO     | app.core.logger:log_file_processing:159 | 文件处理 | ID: 4a256eed-6404-4e6d-8a67-549edb5c423f | 文件: rBIKBmiatqqAXC4lABaSuc9ZQzU547.doc | 大小: 1479353 字节 | 处理时间: 0.000秒
2025-08-12 16:58:39 | INFO     | app.services.file_processor_v2:process_file:548 | 文件处理完成(备用方案): rBIKBmiatqqAXC4lABaSuc9ZQzU547.doc, 输出长度: 36683 字符
2025-08-12 16:58:39 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 优化文件处理流程 | ID: 4a256eed-6404-4e6d-8a67-549edb5c423f | 状态: 成功 | 耗时: 3.491秒
2025-08-12 16:58:39 | INFO     | app.services.compliance_service:process_file_stage:131 | 文件处理完成 | ID: 4a256eed-6404-4e6d-8a67-549edb5c423f | 内容长度: 36683 字符
2025-08-12 16:58:39 | INFO     | app.core.logger:log_file_processing:159 | 文件处理 | ID: 4a256eed-6404-4e6d-8a67-549edb5c423f | 文件: rBIKBmiatqqAXC4lABaSuc9ZQzU547.doc | 大小: 1479353 字节 | 处理时间: 0.000秒
2025-08-12 16:58:39 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | file_processing | ID: 4a256eed-6404-4e6d-8a67-549edb5c423f | 状态: 成功 | 耗时: 3.491秒
2025-08-12 16:58:39 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 文件处理阶段 | ID: 4a256eed-6404-4e6d-8a67-549edb5c423f | 状态: 成功 | 耗时: 3.491秒
2025-08-12 16:58:39 | INFO     | app.services.compliance_service:execute_pipeline:405 | 步骤3: 文件处理完成 | ID: 4a256eed-6404-4e6d-8a67-549edb5c423f | 内容长度: 36683
2025-08-12 16:58:39 | INFO     | app.services.compliance_service:execute_pipeline:416 | 步骤4: 开始AI合规性检查阶段 | ID: 4a256eed-6404-4e6d-8a67-549edb5c423f
2025-08-12 16:58:39 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | AI合规性检查阶段 | ID: 4a256eed-6404-4e6d-8a67-549edb5c423f | 详情: {}
2025-08-12 16:58:39 | INFO     | app.services.compliance_service:ai_compliance_check_stage:188 | 开始AI合规性检查 | ID: 4a256eed-6404-4e6d-8a67-549edb5c423f
2025-08-12 16:58:39 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | AI模型调用(尝试1) | ID: 4a256eed-6404-4e6d-8a67-549edb5c423f | 详情: {}
2025-08-12 16:58:39 | INFO     | app.services.ai_model_service:_call_model_with_retry:770 | 调用AI模型: gemini-2.5-flash, 消息数: 2, 尝试: 1/4 | 请求ID: 4a256eed-6404-4e6d-8a67-549edb5c423f
2025-08-12 16:59:37 | INFO     | app.core.logger:log_api_call:170 | API调用 | OpenAI-gemini-2.5-flash | ID: 4a256eed-6404-4e6d-8a67-549edb5c423f | 状态: 成功 | 耗时: 0.000秒
2025-08-12 16:59:37 | INFO     | app.services.ai_model_service:_call_model_with_retry:809 | AI模型调用成功: 响应长度 5912 字符, 尝试次数: 1 | 请求ID: 4a256eed-6404-4e6d-8a67-549edb5c423f
2025-08-12 16:59:37 | WARNING  | app.services.ai_model_service:_call_model_with_retry:813 | AI模型原始响应内容: '```json
{
  "checkResultArr": [
    {
      "quesType": "合规性",
      "quesDesc": "招标人信息在文件不同位置存在不一致或不明确的情况，可能导致投标人对招标主体产生混淆。原文位置：封面页, 第一章 招标公告 一、招标条件 3, 第一章 招标公告 七、联系方式。原文内容：封面页: “招标人：业主单位”；第一章 招标公告 一、招标条件 3: “招标人：☐ 是 ☑ 否”；第一章 招标公告 七、联系方式: “招标人：安徽江淮汽车集团股份有限公司”。",
      "originalArr": [
        "招标人：业主单位",
        "招标人：☐ 是 ☑ 否",
        "招标人：安徽江淮汽车集团股份有限公司"
      ],
      "point": "项目信息核验",
      "advice": "建议统一招标文件封面、招标公告及联系方式中的招标人名称，确保信息一致且明确，避免使用占位符或不明确的表述。"
    },
    {
      "quesType": "合规性",
      "quesDesc": "招标文件规定投标人提出澄清要求的时间为投标截止时间10日前，此时间可能不足以确保充分的答疑和投标人准备时间。原文位置：第二章 投标人须知 4.1。原文内容：“投标人若对招标文件有任何疑问或澄清要求，应于投标截止时间10日前以书面形式向招标人提出澄清要求”。",
      "originalArr": [
        "投标人若对招标文件有任何疑问或澄清要求，应于投标截止时间10日前以书面形式向招标人提出澄清要求"
      ],
      "point": "答疑机制审核",
      "advice": "建议将投标人提出澄清要求的截止时间延长至投标截止时间15日前，以给予投标人更充足的时间提出疑问和准备投标文件，并与招标人澄清修改的规定相呼应。"
    },
    {
      "quesType": "逻辑性",
      "quesDesc": "合同格式中关于增值税的描述，未明确具体税率，与投标文件格式中明确的13%税率不一致，可能导致合同签订时对税率的理解偏差。原文位置：第三章 合同格式 第二章 价格及付款方式 2.1, 2.2。原文内容：2.1 “本合同价格为包死价，合同含税总价为人民币 元（大写： ），含 增值税；不含税价为人民币 元（大写： ）。如遇国家税率政策调整，合同不含税总价不变。价格具体明细详见附件二：《分项报价清单》。”；2.2 “合同价格包含合同内容的全部费用，以及运输费、管理费、安装调试费、调试所需辅材费、培训服务费、 增值税等。”",
      "originalArr": [
        "2.1 本合同价格为包死价，合同含税总价为人民币     元（大写：       ），含   增值税；不含税价为人民币    元（大写：       ）。如遇国家税率政策调整，合同不含税总价不变。价格具体明细详见附件二：《分项报价清单》。",
        "2.2 合同价格包含合同内容的全部费用，以及运输费、管理费、安装调试费、调试所需辅材费、培训服务费、    增值税等。"
      ],
      "point": "上下文一致性检查",
      "advice": "建议在合同格式中明确增值税税率为13%，与投标文件格式中的税率保持一致，确保合同条款的严谨性和一致性。"
    },
    {
      "quesType": "规范性",
      "quesDesc": "投标文件模板中的投标函部分，投标保证金金额为占位符，未明确要求投标人填写具体金额。原文位置：第四章 投标文件格式 附件格式1. 1．投标函。原文内容：“投标保证金，金额为（金额数） 。”。",
      "originalArr": [
        "投标保证金，金额为（金额数） 。"
      ],
      "point": "文本准确性",
      "advice": "建议在投标函模板中明确提示投标人在此处填写具体的投标保证金金额，或直接引用投标人须知前附表中的金额，避免投标人遗漏或填写错误。"
    },
    {
      "quesType": "规范性",
      "quesDesc": "投标文件模板中的开标一览表部分，项目名称和招标编号/包号为占位符，未明确要求投标人填写。原文位置：第四章 投标文件格式 附件格式2. 2．开标一览表。原文内容：“项目名称： 招标编号/包号：”。",
      "originalArr": [
        "项目名称：                       招标编号/包号："
      ],
      "point": "文本准确性",
      "advice": "建议在开标一览表模板中明确提示投标人填写项目名称和招标编号/包号，或直接预填项目信息，确保投标文件的完整性。"
    },
    {
      "quesType": "规范性",
      "quesDesc": "投标文件模板中的投标分项报价表部分，项目名称和招标编号/包号为占位符，未明确要求投标人填写。原文位置：第四章 投标文件格式 附件格式3. 3．投标分项报价表。原文内容：“项目名称： 招标编号/包号：”。",
      "originalArr": [
        "项目名称：                       招标编号/包号："
      ],
      "point": "文本准确性",
      "advice": "建议在投标分项报价表模板中明确提示投标人填写项目名称和招标编号/包号，或直接预填项目信息，确保投标文件的完整性。"
    },
    {
      "quesType": "规范性",
      "quesDesc": "投标文件模板中的商务条款偏离表部分，招标编号为占位符，未明确要求投标人填写。原文位置：第四章 投标文件格式 附件格式4. 4.商 务 条 款 偏 离 表。原文内容：“招标编号：”。",
      "originalArr": [
        "招标编号："
      ],
      "point": "文本准确性",
      "advice": "建议在商务条款偏离表模板中明确提示投标人填写招标编号，或直接预填项目信息，确保投标文件的完整性。"
    },
    {
      "quesType": "规范性",
      "quesDesc": "投标文件模板中的技术参数偏离表部分，招标编号为占位符，未明确要求投标人填写。原文位置：第四章 投标文件格式 附件格式5. 5.技 术 参 数 偏 离 表。原文内容：“招标编号：”。",
      "originalArr": [
        "招标编号："
      ],
      "point": "文本准确性",
      "advice": "建议在技术参数偏离表模板中明确提示投标人填写招标编号，或直接预填项目信息，确保投标文件的完整性。"
    },
    {
      "quesType": "规范性",
      "quesDesc": "投标文件模板中的关于资格的声明函部分，日期和招标编号/包号为占位符，未明确要求投标人填写。原文位置：第四章 投标文件格式 附件格式6. 6-1 关于资格的声明函。原文内容：“关于贵方 年 月 日第 （招标编号/包号）招标公告”。",
      "originalArr": [
        "关于贵方      年      月      日第                 （招标编号/包号）招标公告"
      ],
      "point": "文本准确性",
      "advice": "建议在关于资格的声明函模板中明确提示投标人填写日期和招标编号/包号，或直接预填项目信息，确保投标文件的完整性。"
    },
    {
      "quesType": "规范性",
      "quesDesc": "投标文件模板中的授权委托书部分，投标人全称、投标人代表姓名、项目名称、招标编号/包号为占位符，未明确要求投标人填写。原文位置：第四章 投标文件格式 附件格式6. 6-3 授权委托书。原文内容：“我公司 （投标人全称）授权 （投标人代表姓名）为投标人代表，参加贵处组织的 项目（招标编号/包号 ）招标活动”。",
      "originalArr": [
        "我公司              （投标人全称）授权          （投标人代表姓名）为投标人代表，参加贵处组织的                  项目（招标编号/包号            ）招标活动"
      ],
      "point": "文本准确性",
      "advice": "建议在授权委托书模板中明确提示投标人填写相关信息，或直接预填项目信息，确保投标文件的完整性。"
    },
    {
      "quesType": "规范性",
      "quesDesc": "投标文件模板中的营业执照声明部分，签发机关名称为占位符，未明确要求投标人填写。原文位置：第四章 投标文件格式 附件格式6. 6-4 营业执照。原文内容：“现附上由 （签发机关名称）签发的我方营业执照副本”。",
      "originalArr": [
        "现附上由                                 （签发机关名称）签发的我方营业执照副本"
      ],
      "point": "文本准确性",
      "advice": "建议在营业执照声明模板中明确提示投标人填写签发机关名称，确保投标文件的完整性。"
    },
    {
      "quesType": "公平性",
      "quesDesc": "评标办法中对“综合实力”评分项的“优秀”、“良好”、“一般”界定过于宽泛，缺乏具体的量化标准或评审细则，可能导致评标委员会在实际评审时主观性过大，影响评审的公平性。原文位置：第六章 评标办法 2.1 商务部分 综合实力。原文内容：“优秀的得（7,10]分，良好的得（3,7]分，一般的得[0,3]分。满分10分。”",
      "originalArr": [
        "优秀的得（7,10]分，良好的得（3,7]分，一般的得[0,3]分。满分10分。"
      ],
      "point": "评分合规性检查",
      "advice": "建议细化“综合实力”评分项的评审标准，明确各分数区间的具体量化指标或详细描述，以减少主观性，确保评审的客观公正。"
    },
    {
      "quesType": "公平性",
      "quesDesc": "评标办法中对“产品技术”评分项的“优秀”、“良好”、“一般”界定过于宽泛，缺乏具体的量化标准或评审细则，可能导致评标委员会在实际评审时主观性过大，影响评审的公平性。原文位置：第六章 评标办法 2.2 技术部分 产品技术。原文内容：“优秀的得（20,30]分，良好的得（10,20]分，一般的得[0,10]分。满分30分。”",
      "originalArr": [
        "优秀的得（20,30]分，良好的得（10,20]分，一般的得[0,10]分。满分30分。"
      ],
      "point": "评分合规性检查",
      "advice": "建议细化“产品技术”评分项的评审标准，明确各分数区间的具体量化指标或详细描述，以减少主观性，确保评审的客观公正。"
    },
    {
      "quesType": "公平性",
      "quesDesc": "评标办法中对“项目管理与实施”评分项的“优秀”、“良好”、“一般”界定过于宽泛，缺乏具体的量化标准或评审细则，可能导致评标委员会在实际评审时主观性过大，影响评审的公平性。原文位置：第六章 评标办法 2.2 技术部分 项目管理与实施。原文内容：“优秀的得（7,10]分，良好的得（3,7]分，一般的得[0,3]分。满分10分。”",
      "originalArr": [
        "优秀的得（7,10]分，良好的得（3,7]分，一般的得[0,3]分。满分10分。"
      ],
      "point": "评分合规性检查",
      "advice": "建议细化“项目管理与实施”评分项的评审标准，明确各分数区间的具体量化指标或详细描述，以减少主观性，确保评审的客观公正。"
    },
    {
      "quesType": "公平性",
      "quesDesc": "评标办法中对“售后服务、培训服务及其他服务承诺”评分项的“优秀”、“良好”、“一般”界定过于宽泛，缺乏具体的量化标准或评审细则，可能导致评标委员会在实际评审时主观性过大，影响评审的公平性。原文位置：第六章 评标办法 2.3 服务部分 售后服务、培训服务及其他服务承诺。原文内容：“优秀的得（7,10]分，良好的得（3,7]分，一般的得[0,3]分。满分10分。”",
      "originalArr": [
        "优秀的得（7,10]分，良好的得（3,7]分，一般的得[0,3]分。满分10分。"
      ],
      "point": "评分合规性检查",
      "advice": "建议细化“售后服务、培训服务及其他服务承诺”评分项的评审标准，明确各分数区间的具体量化指标或详细描述，以减少主观性，确保评审的客观公正。"
    }
  ]
}
```'
2025-08-12 16:59:37 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | AI模型调用(尝试1) | ID: 4a256eed-6404-4e6d-8a67-549edb5c423f | 状态: 成功 | 耗时: 57.310秒
2025-08-12 16:59:37 | INFO     | app.services.ai_model_service:check_compliance:1090 | 开始清理AI响应JSON数据 | 请求ID: 4a256eed-6404-4e6d-8a67-549edb5c423f | 原始长度: 5912 字符
2025-08-12 16:59:37 | INFO     | app.services.ai_model_service:check_compliance:1097 | JSON数据清理完成 | 请求ID: 4a256eed-6404-4e6d-8a67-549edb5c423f | 清理后长度: 5759 字符
2025-08-12 16:59:37 | INFO     | app.services.ai_model_service:check_compliance:1104 | 开始解析清理后的JSON数据 | 请求ID: 4a256eed-6404-4e6d-8a67-549edb5c423f
2025-08-12 16:59:37 | INFO     | app.services.ai_model_service:check_compliance:1106 | JSON解析成功 | 请求ID: 4a256eed-6404-4e6d-8a67-549edb5c423f | 数据类型: <class 'dict'>
2025-08-12 16:59:37 | INFO     | app.services.ai_model_service:check_compliance:1124 | 开始解析AI返回的检查结果，共 15 个项目
2025-08-12 16:59:37 | INFO     | app.services.ai_model_service:check_compliance:1180 | 合规性检查完成，发现 15 个问题
2025-08-12 16:59:37 | INFO     | app.services.compliance_service:ai_compliance_check_stage:195 | AI合规性检查完成 | ID: 4a256eed-6404-4e6d-8a67-549edb5c423f | 发现问题: 15个
2025-08-12 16:59:37 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | ai_model_call | ID: 4a256eed-6404-4e6d-8a67-549edb5c423f | 状态: 成功 | 耗时: 57.312秒
2025-08-12 16:59:37 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | AI合规性检查阶段 | ID: 4a256eed-6404-4e6d-8a67-549edb5c423f | 状态: 成功 | 耗时: 57.312秒
2025-08-12 16:59:37 | INFO     | app.services.compliance_service:execute_pipeline:423 | 步骤4: AI合规性检查完成 | ID: 4a256eed-6404-4e6d-8a67-549edb5c423f | 结果数量: 15
2025-08-12 16:59:37 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 敏感词检测阶段 | ID: 4a256eed-6404-4e6d-8a67-549edb5c423f | 详情: {}
2025-08-12 16:59:37 | INFO     | app.services.compliance_service:sensitive_word_check_stage:245 | 开始敏感词检测 | ID: 4a256eed-6404-4e6d-8a67-549edb5c423f
2025-08-12 16:59:37 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 敏感词服务健康检查 | ID: 4a256eed-6404-4e6d-8a67-549edb5c423f | 详情: {}
2025-08-12 16:59:37 | INFO     | app.services.sensitive_word_service:check_health:122 | 检查敏感词服务健康状态: http://************:8087/health
2025-08-12 16:59:37 | INFO     | app.services.sensitive_word_service:check_health:132 | 敏感词服务健康状态: 健康
2025-08-12 16:59:37 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 敏感词服务健康检查 | ID: 4a256eed-6404-4e6d-8a67-549edb5c423f | 状态: 成功 | 耗时: 0.012秒
2025-08-12 16:59:37 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 敏感词检测 | ID: 4a256eed-6404-4e6d-8a67-549edb5c423f | 详情: {}
2025-08-12 16:59:37 | INFO     | app.services.sensitive_word_service:detect_sensitive_words:214 | 开始敏感词检测: 内容长度=36683, 政府采购=False
2025-08-12 16:59:37 | INFO     | app.services.sensitive_word_service:detect_sensitive_words:223 | 敏感词检测超时设置: 60.0秒 | ID: 4a256eed-6404-4e6d-8a67-549edb5c423f
2025-08-12 16:59:37 | INFO     | app.services.sensitive_word_service:_convert_to_target_format:376 | 敏感词转换完成: 原始3个，有效3个
2025-08-12 16:59:37 | INFO     | app.core.logger:log_api_call:170 | API调用 | 敏感词检测API | ID: 4a256eed-6404-4e6d-8a67-549edb5c423f | 状态: 成功 | 耗时: 0.000秒
2025-08-12 16:59:37 | INFO     | app.services.sensitive_word_service:detect_sensitive_words:269 | 敏感词检测完成: 发现 3 个敏感词
2025-08-12 16:59:37 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 敏感词检测 | ID: 4a256eed-6404-4e6d-8a67-549edb5c423f | 状态: 成功 | 耗时: 0.023秒
2025-08-12 16:59:37 | INFO     | app.services.compliance_service:sensitive_word_check_stage:252 | 敏感词检测完成 | ID: 4a256eed-6404-4e6d-8a67-549edb5c423f | 发现敏感词: 3个
2025-08-12 16:59:37 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | sensitive_word | ID: 4a256eed-6404-4e6d-8a67-549edb5c423f | 状态: 成功 | 耗时: 0.037秒
2025-08-12 16:59:37 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 敏感词检测阶段 | ID: 4a256eed-6404-4e6d-8a67-549edb5c423f | 状态: 成功 | 耗时: 0.037秒
2025-08-12 16:59:37 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 结果聚合阶段 | ID: 4a256eed-6404-4e6d-8a67-549edb5c423f | 详情: {}
2025-08-12 16:59:37 | INFO     | app.services.compliance_service:result_aggregation_stage:305 | 开始结果聚合 | ID: 4a256eed-6404-4e6d-8a67-549edb5c423f
2025-08-12 16:59:37 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 结果聚合 | ID: 4a256eed-6404-4e6d-8a67-549edb5c423f | 详情: {}
2025-08-12 16:59:37 | INFO     | app.services.result_processor:aggregate_results:334 | 开始结果聚合: 敏感词3个，检查结果15个 | ID: 4a256eed-6404-4e6d-8a67-549edb5c423f
2025-08-12 16:59:37 | INFO     | app.services.result_processor:validate_sensitive_words:109 | 敏感词验证完成: 原始3个，有效3个
2025-08-12 16:59:37 | INFO     | app.services.result_processor:deduplicate_sensitive_words:245 | 敏感词去重完成: 原始3个，去重后3个
2025-08-12 16:59:37 | INFO     | app.services.result_processor:_verify_deduplication_integrity:954 | 去重完整性验证 | ID: 4a256eed-6404-4e6d-8a67-549edb5c423f | 状态: 完美 | 输入: 3个 | 输出: 3个 | 出现次数变化: 50 -> 50
2025-08-12 16:59:37 | INFO     | app.services.result_processor:validate_check_results:199 | 检查结果验证完成: 原始15个，有效15个
2025-08-12 16:59:37 | INFO     | app.services.result_processor:prioritize_check_results:283 | 检查结果排序完成: 15个结果
2025-08-12 16:59:37 | INFO     | app.services.result_processor:_verify_final_integrity:754 | 完整性报告 | ID: 4a256eed-6404-4e6d-8a67-549edb5c423f | 敏感词保留率: 100.0% | 检查结果保留率: 100.0%
2025-08-12 16:59:37 | INFO     | app.services.result_processor:aggregate_results:396 | 结果聚合完成: 敏感词3个，检查结果15个 | ID: 4a256eed-6404-4e6d-8a67-549edb5c423f
2025-08-12 16:59:37 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 结果聚合 | ID: 4a256eed-6404-4e6d-8a67-549edb5c423f | 状态: 成功 | 耗时: 0.004秒
2025-08-12 16:59:37 | INFO     | app.services.result_processor:validate_final_response:490 | 响应验证完成: 敏感词3个，检查结果15个
2025-08-12 16:59:37 | INFO     | app.services.compliance_service:result_aggregation_stage:312 | 结果聚合完成 | ID: 4a256eed-6404-4e6d-8a67-549edb5c423f | 敏感词: 3个 | 检查结果: 15个
2025-08-12 16:59:37 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | result_aggregation | ID: 4a256eed-6404-4e6d-8a67-549edb5c423f | 状态: 成功 | 耗时: 0.006秒
2025-08-12 16:59:37 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 结果聚合阶段 | ID: 4a256eed-6404-4e6d-8a67-549edb5c423f | 状态: 成功 | 耗时: 0.006秒
2025-08-12 16:59:37 | INFO     | app.services.compliance_service:_add_pipeline_metadata:538 | 流水线元数据 | ID: 4a256eed-6404-4e6d-8a67-549edb5c423f | 处理时间: 61.024秒 | 降级使用: False | 敏感词数量: 3 | 检查结果数量: 15
2025-08-12 16:59:37 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | total_pipeline | ID: 4a256eed-6404-4e6d-8a67-549edb5c423f | 状态: 成功 | 耗时: 61.024秒
2025-08-12 16:59:37 | INFO     | app.services.compliance_service:execute_pipeline:479 | 合规性检查流水线完成 | ID: 4a256eed-6404-4e6d-8a67-549edb5c423f | 耗时: 61.024秒 | 降级: False
2025-08-12 16:59:37 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 完整合规性检查流水线 | ID: 4a256eed-6404-4e6d-8a67-549edb5c423f | 状态: 成功 | 耗时: 61.026秒
2025-08-12 16:59:37 | INFO     | app.services.compliance_service:check_compliance:641 | 合规性检查服务完成 | ID: 4a256eed-6404-4e6d-8a67-549edb5c423f
2025-08-12 16:59:37 | INFO     | app.api.routes:check_compliance_simple:156 | 简化合规性检查完成 | ID: 4a256eed-6404-4e6d-8a67-549edb5c423f | 文件: rBIKBmiatqqAXC4lABaSuc9ZQzU547.doc | 敏感词: 3个 | 检查结果: 15个
2025-08-12 16:59:37 | INFO     | app.middleware.logging:dispatch:48 | 请求完成 | ID: 4a256eed-6404-4e6d-8a67-549edb5c423f | 状态码: 200 | 耗时: 62.047秒
2025-08-12 17:02:17 | INFO     | main:lifespan:47 | 招标文件合规性检查助手关闭中...
2025-08-12 17:02:17 | INFO     | main:lifespan:51 | 停止异步请求队列...
2025-08-12 17:02:17 | INFO     | app.core.queue_manager:stop_workers:125 | 停止请求队列工作线程
2025-08-12 17:02:17 | INFO     | app.core.queue_manager:_worker:296 | 工作线程被取消: worker-0
2025-08-12 17:02:17 | INFO     | app.core.queue_manager:_worker:301 | 工作线程停止: worker-0
2025-08-12 17:02:17 | INFO     | app.core.queue_manager:_worker:296 | 工作线程被取消: worker-1
2025-08-12 17:02:17 | INFO     | app.core.queue_manager:_worker:301 | 工作线程停止: worker-1
2025-08-12 17:02:17 | INFO     | app.core.queue_manager:_worker:296 | 工作线程被取消: worker-2
2025-08-12 17:02:17 | INFO     | app.core.queue_manager:_worker:301 | 工作线程停止: worker-2
2025-08-12 17:02:17 | INFO     | app.core.queue_manager:_worker:296 | 工作线程被取消: worker-3
2025-08-12 17:02:17 | INFO     | app.core.queue_manager:_worker:301 | 工作线程停止: worker-3
2025-08-12 17:02:17 | INFO     | main:lifespan:53 | 请求队列已停止
2025-08-12 17:02:17 | INFO     | app.core.cache_manager:clear:198 | 缓存已清空
2025-08-12 17:02:17 | INFO     | main:lifespan:58 | 缓存已清理
2025-08-12 17:02:17 | INFO     | main:lifespan:60 | 招标文件合规性检查助手已关闭
