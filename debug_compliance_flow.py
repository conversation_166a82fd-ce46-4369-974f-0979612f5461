#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试合规性检查流程的具体步骤
"""

import requests
import json
import time
import threading


def test_with_timeout_monitoring():
    """带超时监控的合规性检查测试"""
    print("带超时监控的合规性检查测试")
    print("=" * 50)

    base_url = "http://localhost:8088"

    # 使用一个会快速失败的URL，避免文件下载耗时
    request_data = {
        "procurement_project_type": "服务类",
        "project_category": "政府采购",
        "bidding_procurement_method": "公开招标",
        "bidding_doc": {
            "filename": "test.docx",
            "extension": ".docx",
            "mime_type": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "size": 100,
            "url": "http://httpbin.org/status/404",  # 会立即返回404
        },
    }

    print("发送请求...")
    print("监控处理进度...")

    # 创建一个监控线程
    stop_monitoring = threading.Event()

    def monitor_progress():
        """监控处理进度"""
        start_time = time.time()
        while not stop_monitoring.is_set():
            elapsed = time.time() - start_time
            print(f"已等待: {elapsed:.1f}秒", end="\r")
            time.sleep(1)

    monitor_thread = threading.Thread(target=monitor_progress)
    monitor_thread.start()

    try:
        start_time = time.time()

        response = requests.post(
            f"{base_url}/api/v1/check-compliance",
            json=request_data,
            timeout=60,  # 60秒超时
        )

        elapsed = time.time() - start_time
        stop_monitoring.set()
        monitor_thread.join()

        print(f"\n处理完成，耗时: {elapsed:.2f}秒")
        print(f"状态码: {response.status_code}")

        # 显示响应内容
        response_text = response.text
        if len(response_text) > 1000:
            response_text = response_text[:1000] + "..."

        print(f"响应内容: {response_text}")

        # 尝试解析JSON
        try:
            if response.headers.get("content-type", "").startswith("application/json"):
                data = response.json()
                print(f"JSON解析成功: {json.dumps(data, indent=2, ensure_ascii=False)}")
        except:
            print("响应不是有效的JSON")

        return response.status_code == 200

    except requests.exceptions.Timeout:
        elapsed = time.time() - start_time
        stop_monitoring.set()
        monitor_thread.join()
        print(f"\n❌ 请求超时，耗时: {elapsed:.2f}秒")
        return False

    except Exception as e:
        stop_monitoring.set()
        monitor_thread.join()
        print(f"\n❌ 请求异常: {str(e)}")
        return False


def test_parameter_validation():
    """测试参数验证"""
    print("\n测试参数验证")
    print("=" * 50)

    base_url = "http://localhost:8088"

    # 测试无效的参数
    invalid_request = {
        "procurement_project_type": "无效类型",  # 无效的枚举值
        "project_category": "政府采购",
        "bidding_procurement_method": "公开招标",
        "bidding_doc": {
            "filename": "test.docx",
            "extension": ".docx",
            "mime_type": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "size": 100,
            "url": "http://example.com/test.docx",
        },
    }

    try:
        print("发送无效参数请求...")
        response = requests.post(
            f"{base_url}/api/v1/check-compliance", json=invalid_request, timeout=10
        )

        print(f"状态码: {response.status_code}")
        print(f"响应: {response.text}")

        if response.status_code == 400:
            print("✅ 参数验证正常工作")
            return True
        else:
            print("❌ 参数验证可能有问题")
            return False

    except Exception as e:
        print(f"❌ 参数验证测试失败: {str(e)}")
        return False


def test_step_by_step_simulation():
    """模拟逐步处理"""
    print("\n模拟逐步处理")
    print("=" * 50)

    base_url = "http://localhost:8088"

    # 1. 测试文件验证
    print("1. 测试文件验证...")
    file_info = {
        "filename": "test.docx",
        "extension": ".docx",
        "mime_type": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "size": 100,
        "url": "http://example.com/test.docx",
    }

    try:
        response = requests.post(
            f"{base_url}/api/v1/validate-file", json=file_info, timeout=10
        )
        print(f"   文件验证状态码: {response.status_code}")
        if response.status_code != 200:
            print(f"   文件验证失败: {response.text}")
            return False
        else:
            print("   ✅ 文件验证正常")
    except Exception as e:
        print(f"   ❌ 文件验证异常: {str(e)}")
        return False

    # 2. 测试敏感词检测
    print("2. 测试敏感词检测...")
    try:
        response = requests.get(f"{base_url}/api/v1/sensitive-word-stats", timeout=10)
        print(f"   敏感词服务状态码: {response.status_code}")
        if response.status_code == 200:
            print("   ✅ 敏感词服务正常")
        else:
            print(f"   ❌ 敏感词服务异常: {response.text}")
    except Exception as e:
        print(f"   ❌ 敏感词服务测试异常: {str(e)}")

    return True


def main():
    """主函数"""
    print("合规性检查流程详细调试")
    print("=" * 60)

    # 1. 测试参数验证
    param_ok = test_parameter_validation()

    # 2. 测试逐步模拟
    step_ok = test_step_by_step_simulation()

    # 3. 测试带监控的合规性检查
    if param_ok and step_ok:
        compliance_ok = test_with_timeout_monitoring()
    else:
        print("跳过合规性检查测试，因为前置测试失败")
        compliance_ok = False

    print(f"\n{'='*60}")
    print("调试总结")
    print("=" * 60)
    print(f"参数验证: {'✅' if param_ok else '❌'}")
    print(f"逐步模拟: {'✅' if step_ok else '❌'}")
    print(f"合规性检查: {'✅' if compliance_ok else '❌'}")

    if not compliance_ok:
        print(f"\n🔍 问题可能出现在:")
        print("1. 文件下载阶段 - 即使是404错误也可能卡住")
        print("2. AI模型调用阶段 - 虽然连接正常，但处理可能超时")
        print("3. 敏感词检测阶段 - 大量文本处理可能耗时")
        print("4. 结果处理阶段 - 数据聚合可能有死循环")
        print("5. 中间件处理 - 请求验证或日志记录可能卡住")

        print(f"\n💡 建议的解决方案:")
        print("1. 在合规性检查流程中添加更多日志")
        print("2. 为每个处理阶段设置独立的超时")
        print("3. 添加处理进度回调")
        print("4. 临时禁用某些组件来隔离问题")


if __name__ == "__main__":
    main()
